{"version": 3, "sources": [], "sections": [{"offset": {"line": 23, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/packages/cms/keys.ts"], "sourcesContent": ["import { createEnv } from '@t3-oss/env-nextjs';\nimport { z } from 'zod';\n\nexport const keys = () =>\n  createEnv({\n    server: {\n      BASEHUB_TOKEN: z.string().startsWith('bshb_pk_'),\n    },\n    runtimeEnv: {\n      BASEHUB_TOKEN: process.env.BASEHUB_TOKEN,\n    },\n  });\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEO,MAAM,OAAO,IAClB,CAAA,GAAA,kRAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ;YACN,eAAe,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,UAAU,CAAC;QACvC;QACA,YAAY;YACV,eAAe,QAAQ,GAAG,CAAC,aAAa;QAC1C;IACF"}}, {"offset": {"line": 45, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/packages/email/keys.ts"], "sourcesContent": ["import { createEnv } from '@t3-oss/env-nextjs';\nimport { z } from 'zod';\n\nexport const keys = () =>\n  createEnv({\n    server: {\n      RESEND_FROM: z.string().email(),\n      RESEND_TOKEN: z.string().startsWith('re_'),\n    },\n    runtimeEnv: {\n      RESEND_FROM: process.env.RESEND_FROM,\n      RESEND_TOKEN: process.env.RESEND_TOKEN,\n    },\n  });\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEO,MAAM,OAAO,IAClB,CAAA,GAAA,kRAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ;YACN,aAAa,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,KAAK;YAC7B,cAAc,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,UAAU,CAAC;QACtC;QACA,YAAY;YACV,aAAa,QAAQ,GAAG,CAAC,WAAW;YACpC,cAAc,QAAQ,GAAG,CAAC,YAAY;QACxC;IACF"}}, {"offset": {"line": 69, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/packages/feature-flags/keys.ts"], "sourcesContent": ["import { createEnv } from '@t3-oss/env-nextjs';\nimport { z } from 'zod';\n\nexport const keys = () =>\n  createEnv({\n    server: {\n      FLAGS_SECRET: z.string().optional(),\n    },\n    runtimeEnv: {\n      FLAGS_SECRET: process.env.FLAGS_SECRET,\n    },\n  });\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEO,MAAM,OAAO,IAClB,CAAA,GAAA,kRAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ;YACN,cAAc,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACnC;QACA,YAAY;YACV,cAAc,QAAQ,GAAG,CAAC,YAAY;QACxC;IACF"}}, {"offset": {"line": 91, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/packages/next-config/keys.ts"], "sourcesContent": ["import { vercel } from '@t3-oss/env-core/presets-zod';\nimport { createEnv } from '@t3-oss/env-nextjs';\nimport { z } from 'zod';\n\nexport const keys = () =>\n  createEnv({\n    extends: [vercel()],\n    server: {\n      ANALYZE: z.string().optional(),\n\n      // Added by Vercel\n      NEXT_RUNTIME: z.enum(['nodejs', 'edge']).optional(),\n    },\n    client: {\n      NEXT_PUBLIC_APP_URL: z.string().url(),\n      NEXT_PUBLIC_WEB_URL: z.string().url(),\n      NEXT_PUBLIC_API_URL: z.string().url().optional(),\n      NEXT_PUBLIC_DOCS_URL: z.string().url().optional(),\n    },\n    runtimeEnv: {\n      ANALYZE: process.env.ANALYZE,\n      NEXT_RUNTIME: process.env.NEXT_RUNTIME,\n      NEXT_PUBLIC_APP_URL: process.env.NEXT_PUBLIC_APP_URL,\n      NEXT_PUBLIC_WEB_URL: process.env.NEXT_PUBLIC_WEB_URL,\n      NEXT_PUBLIC_API_URL: process.env.NEXT_PUBLIC_API_URL,\n      NEXT_PUBLIC_DOCS_URL: process.env.NEXT_PUBLIC_DOCS_URL,\n    },\n  });\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AAAA;;;;AAEO,MAAM,OAAO,IAClB,CAAA,GAAA,kRAAA,CAAA,YAAS,AAAD,EAAE;QACR,SAAS;YAAC,CAAA,GAAA,yRAAA,CAAA,SAAM,AAAD;SAAI;QACnB,QAAQ;YACN,SAAS,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YAE5B,kBAAkB;YAClB,cAAc,2OAAA,CAAA,IAAC,CAAC,IAAI,CAAC;gBAAC;gBAAU;aAAO,EAAE,QAAQ;QACnD;QACA,QAAQ;YACN,qBAAqB,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG;YACnC,qBAAqB,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG;YACnC,qBAAqB,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ;YAC9C,sBAAsB,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ;QACjD;QACA,YAAY;YACV,SAAS,QAAQ,GAAG,CAAC,OAAO;YAC5B,YAAY;YACZ,mBAAmB;YACnB,mBAAmB;YACnB,mBAAmB;YACnB,oBAAoB;QACtB;IACF"}}, {"offset": {"line": 134, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/packages/observability/keys.ts"], "sourcesContent": ["import { createEnv } from '@t3-oss/env-nextjs';\nimport { z } from 'zod';\n\nexport const keys = () =>\n  createEnv({\n    server: {\n      BETTERSTACK_API_KEY: z.string().optional(),\n      BETTERSTACK_URL: z.string().optional(),\n\n      // Added by Sentry Integration, Vercel Marketplace\n      SENTRY_ORG: z.string().optional(),\n      SENTRY_PROJECT: z.string().optional(),\n    },\n    client: {\n      // Added by Sentry Integration, Vercel Marketplace\n      NEXT_PUBLIC_SENTRY_DSN: z.string().url().optional(),\n    },\n    runtimeEnv: {\n      BETTERSTACK_API_KEY: process.env.BETTERSTACK_API_KEY,\n      BETTERSTACK_URL: process.env.BETTERSTACK_URL,\n      SENTRY_ORG: process.env.SENTRY_ORG,\n      SENTRY_PROJECT: process.env.SENTRY_PROJECT,\n      NEXT_PUBLIC_SENTRY_DSN: process.env.NEXT_PUBLIC_SENTRY_DSN,\n    },\n  });\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEO,MAAM,OAAO,IAClB,CAAA,GAAA,kRAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ;YACN,qBAAqB,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YACxC,iBAAiB,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YAEpC,kDAAkD;YAClD,YAAY,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;YAC/B,gBAAgB,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QACrC;QACA,QAAQ;YACN,kDAAkD;YAClD,wBAAwB,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ;QACnD;QACA,YAAY;YACV,qBAAqB,QAAQ,GAAG,CAAC,mBAAmB;YACpD,iBAAiB,QAAQ,GAAG,CAAC,eAAe;YAC5C,YAAY,QAAQ,GAAG,CAAC,UAAU;YAClC,gBAAgB,QAAQ,GAAG,CAAC,cAAc;YAC1C,wBAAwB,QAAQ,GAAG,CAAC,sBAAsB;QAC5D;IACF"}}, {"offset": {"line": 168, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/packages/rate-limit/keys.ts"], "sourcesContent": ["import { createEnv } from '@t3-oss/env-nextjs';\nimport { z } from 'zod';\n\nexport const keys = () =>\n  createEnv({\n    server: {\n      UPSTASH_REDIS_REST_URL: z.string().url().optional(),\n      UPSTASH_REDIS_REST_TOKEN: z.string().optional(),\n    },\n    runtimeEnv: {\n      UPSTASH_REDIS_REST_URL: process.env.UPSTASH_REDIS_REST_URL,\n      UPSTASH_REDIS_REST_TOKEN: process.env.UPSTASH_REDIS_REST_TOKEN,\n    },\n  });\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEO,MAAM,OAAO,IAClB,CAAA,GAAA,kRAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ;YACN,wBAAwB,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,GAAG,GAAG,QAAQ;YACjD,0BAA0B,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,QAAQ;QAC/C;QACA,YAAY;YACV,wBAAwB,QAAQ,GAAG,CAAC,sBAAsB;YAC1D,0BAA0B,QAAQ,GAAG,CAAC,wBAAwB;QAChE;IACF"}}, {"offset": {"line": 192, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/packages/security/keys.ts"], "sourcesContent": ["import { createEnv } from '@t3-oss/env-nextjs';\nimport { z } from 'zod';\n\nexport const keys = () =>\n  createEnv({\n    server: {\n      ARCJET_KEY: z.string().startsWith('ajkey_').optional(),\n    },\n    runtimeEnv: {\n      ARCJET_KEY: process.env.ARCJET_KEY,\n    },\n  });\n"], "names": [], "mappings": ";;;AAAA;AACA;AAAA;;;AAEO,MAAM,OAAO,IAClB,CAAA,GAAA,kRAAA,CAAA,YAAS,AAAD,EAAE;QACR,QAAQ;YACN,YAAY,2OAAA,CAAA,IAAC,CAAC,MAAM,GAAG,UAAU,CAAC,UAAU,QAAQ;QACtD;QACA,YAAY;YACV,YAAY,QAAQ,GAAG,CAAC,UAAU;QACpC;IACF"}}, {"offset": {"line": 214, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/apps/web/env.ts"], "sourcesContent": ["import { keys as cms } from '@repo/cms/keys';\nimport { keys as email } from '@repo/email/keys';\nimport { keys as flags } from '@repo/feature-flags/keys';\nimport { keys as core } from '@repo/next-config/keys';\nimport { keys as observability } from '@repo/observability/keys';\nimport { keys as rateLimit } from '@repo/rate-limit/keys';\nimport { keys as security } from '@repo/security/keys';\nimport { createEnv } from '@t3-oss/env-nextjs';\n\nexport const env = createEnv({\n  extends: [\n    cms(),\n    core(),\n    email(),\n    observability(),\n    flags(),\n    security(),\n    rateLimit(),\n  ],\n  server: {},\n  client: {},\n  runtimeEnv: {},\n});\n"], "names": [], "mappings": ";;;AAAA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;;;;;;;;AAEO,MAAM,MAAM,CAAA,GAAA,kRAAA,CAAA,YAAS,AAAD,EAAE;IAC3B,SAAS;QACP,CAAA,GAAA,+HAAA,CAAA,OAAG,AAAD;QACF,CAAA,GAAA,0IAAA,CAAA,OAAI,AAAD;QACH,CAAA,GAAA,iIAAA,CAAA,OAAK,AAAD;QACJ,CAAA,GAAA,yIAAA,CAAA,OAAa,AAAD;QACZ,CAAA,GAAA,4IAAA,CAAA,OAAK,AAAD;QACJ,CAAA,GAAA,oIAAA,CAAA,OAAQ,AAAD;QACP,CAAA,GAAA,yIAAA,CAAA,OAAS,AAAD;KACT;IACD,QAAQ,CAAC;IACT,QAAQ,CAAC;IACT,YAAY,CAAC;AACf"}}, {"offset": {"line": 253, "column": 0}, "map": {"version": 3, "sources": [], "names": [], "mappings": ""}}, {"offset": {"line": 269, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/packages/security/middleware.ts"], "sourcesContent": ["import {\n  type NoseconeOptions,\n  defaults,\n  withVercelToolbar,\n} from '@nosecone/next';\nexport { createMiddleware as noseconeMiddleware } from '@nosecone/next';\n\n// Nosecone security headers configuration\n// https://docs.arcjet.com/nosecone/quick-start\nexport const noseconeOptions: NoseconeOptions = {\n  ...defaults,\n  // Content Security Policy (CSP) is disabled by default because the values\n  // depend on which Next Forge features are enabled. See\n  // https://www.next-forge.com/packages/security/headers for guidance on how\n  // to configure it.\n  contentSecurityPolicy: false,\n};\n\nexport const noseconeOptionsWithToolbar: NoseconeOptions =\n  withVercelToolbar(noseconeOptions);\n"], "names": [], "mappings": ";;;;AAAA;AAAA;AAAA;;;AASO,MAAM,kBAAmC;IAC9C,GAAG,gRAAA,CAAA,WAAQ;IACX,0EAA0E;IAC1E,uDAAuD;IACvD,2EAA2E;IAC3E,mBAAmB;IACnB,uBAAuB;AACzB;AAEO,MAAM,6BACX,CAAA,GAAA,2MAAA,CAAA,oBAAiB,AAAD,EAAE"}}, {"offset": {"line": 302, "column": 0}, "map": {"version": 3, "sources": ["turbopack:///[project]/apps/web/middleware.ts"], "sourcesContent": ["import { env } from '@/env';\nimport { authMiddleware } from '@repo/auth/middleware';\nimport { internationalizationMiddleware } from '@repo/internationalization/middleware';\nimport {\n  noseconeMiddleware,\n  noseconeOptions,\n  noseconeOptionsWithToolbar,\n} from '@repo/security/middleware';\nimport {\n  type NextMiddleware,\n  type NextRequest,\n  NextResponse,\n} from 'next/server';\n\nexport const config = {\n  // matcher tells Next.js which routes to run the middleware on. This runs the\n  // middleware on all routes except for static assets and Posthog ingest\n  matcher: ['/((?!_next/static|_next/image|images|ingest|favicon.ico).*)'],\n};\n\nconst securityHeaders = env.FLAGS_SECRET\n  ? noseconeMiddleware(noseconeOptionsWithToolbar)\n  : noseconeMiddleware(noseconeOptions);\n\nconst middleware = authMiddleware(async (_auth, request) => {\n  // Internationalization disabled - no longer using locale routing\n  // const i18nResponse = internationalizationMiddleware(\n  //   request as unknown as NextRequest\n  // );\n  // if (i18nResponse) {\n  //   return i18nResponse;\n  // }\n\n  // Skip Arcjet for now to reduce middleware size\n  // TODO: Re-enable when middleware size limit is increased or Arcjet is optimized\n  // if (!env.ARCJET_KEY) {\n    return securityHeaders();\n  // }\n\n  // Arcjet security disabled to reduce middleware bundle size\n  // try {\n  //   const { secure } = await import('@repo/security');\n  //   await secure(\n  //     [\n  //       // See https://docs.arcjet.com/bot-protection/identifying-bots\n  //       'CATEGORY:SEARCH_ENGINE', // Allow search engines\n  //       'CATEGORY:PREVIEW', // Allow preview links to show OG images\n  //       'CATEGORY:MONITOR', // Allow uptime monitoring services\n  //     ],\n  //     request\n  //   );\n  //   return securityHeaders();\n  // } catch (error) {\n  //   const { parseError } = await import('@repo/observability/error');\n  //   const message = parseError(error);\n  //   return NextResponse.json({ error: message }, { status: 403 });\n  // }\n}) as unknown as NextMiddleware;\n\nexport default middleware;\n"], "names": [], "mappings": ";;;;AAAA;AACA;AAAA;AAEA;AAAA;AAAA;;;;AAWO,MAAM,SAAS;IACpB,6EAA6E;IAC7E,uEAAuE;IACvE,SAAS;QAAC;KAA8D;AAC1E;AAEA,MAAM,kBAAkB,0HAAA,CAAA,MAAG,CAAC,YAAY,GACpC,CAAA,GAAA,0UAAA,CAAA,qBAAkB,AAAD,EAAE,0JAAA,CAAA,6BAA0B,IAC7C,CAAA,GAAA,0UAAA,CAAA,qBAAkB,AAAD,EAAE,0JAAA,CAAA,kBAAe;AAEtC,MAAM,aAAa,CAAA,GAAA,oVAAA,CAAA,iBAAc,AAAD,EAAE,OAAO,OAAO;IAC9C,iEAAiE;IACjE,uDAAuD;IACvD,sCAAsC;IACtC,KAAK;IACL,sBAAsB;IACtB,yBAAyB;IACzB,IAAI;IAEJ,gDAAgD;IAChD,iFAAiF;IACjF,yBAAyB;IACvB,OAAO;AACT,IAAI;AAEJ,4DAA4D;AAC5D,QAAQ;AACR,uDAAuD;AACvD,kBAAkB;AAClB,QAAQ;AACR,uEAAuE;AACvE,0DAA0D;AAC1D,qEAAqE;AACrE,gEAAgE;AAChE,SAAS;AACT,cAAc;AACd,OAAO;AACP,8BAA8B;AAC9B,oBAAoB;AACpB,sEAAsE;AACtE,uCAAuC;AACvC,mEAAmE;AACnE,IAAI;AACN;uCAEe"}}]}