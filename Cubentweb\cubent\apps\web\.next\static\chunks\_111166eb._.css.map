{"version": 3, "sources": [], "sections": [{"offset": {"line": 1, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/styles.css"], "sourcesContent": ["/*! tailwindcss v4.1.7 | MIT License | https://tailwindcss.com */\n@layer properties;\n@layer theme, base, components, utilities;\n@layer theme {\n  :root, :host {\n    --color-red-500: oklch(63.7% 0.237 25.331);\n    --color-red-600: oklch(57.7% 0.245 27.325);\n    --color-orange-300: oklch(83.7% 0.128 66.29);\n    --color-orange-400: oklch(75% 0.183 55.934);\n    --color-orange-500: oklch(70.5% 0.213 47.604);\n    --color-orange-600: oklch(64.6% 0.222 41.116);\n    --color-orange-700: oklch(55.3% 0.195 38.402);\n    --color-orange-800: oklch(47% 0.157 37.304);\n    --color-orange-950: oklch(26.6% 0.079 36.259);\n    --color-yellow-400: oklch(85.2% 0.199 91.936);\n    --color-green-400: oklch(79.2% 0.209 151.711);\n    --color-green-500: oklch(72.3% 0.219 149.579);\n    --color-cyan-400: oklch(78.9% 0.154 211.53);\n    --color-cyan-500: oklch(71.5% 0.143 215.221);\n    --color-blue-400: oklch(70.7% 0.165 254.624);\n    --color-blue-500: oklch(62.3% 0.214 259.815);\n    --color-blue-600: oklch(54.6% 0.245 262.881);\n    --color-blue-700: oklch(48.8% 0.243 264.376);\n    --color-purple-500: oklch(62.7% 0.265 303.9);\n    --color-purple-600: oklch(55.8% 0.288 302.321);\n    --color-pink-500: oklch(65.6% 0.241 354.308);\n    --color-gray-200: oklch(92.8% 0.006 264.531);\n    --color-gray-300: oklch(87.2% 0.01 258.338);\n    --color-gray-400: oklch(70.7% 0.022 261.325);\n    --color-gray-500: oklch(55.1% 0.027 264.364);\n    --color-gray-600: oklch(44.6% 0.03 256.802);\n    --color-gray-700: oklch(37.3% 0.034 259.733);\n    --color-gray-800: oklch(27.8% 0.033 256.848);\n    --color-gray-900: oklch(21% 0.034 264.665);\n    --color-neutral-300: oklch(87% 0 0);\n    --color-neutral-400: oklch(70.8% 0 0);\n    --color-neutral-600: oklch(43.9% 0 0);\n    --color-neutral-700: oklch(37.1% 0 0);\n    --color-neutral-800: oklch(26.9% 0 0);\n    --color-neutral-900: oklch(20.5% 0 0);\n    --color-black: #000;\n    --color-white: #fff;\n    --spacing: 0.25rem;\n    --container-xs: 20rem;\n    --container-sm: 24rem;\n    --container-md: 28rem;\n    --container-lg: 32rem;\n    --container-xl: 36rem;\n    --container-2xl: 42rem;\n    --container-3xl: 48rem;\n    --container-4xl: 56rem;\n    --container-5xl: 64rem;\n    --container-6xl: 72rem;\n    --container-7xl: 80rem;\n    --text-xs: 0.75rem;\n    --text-xs--line-height: calc(1 / 0.75);\n    --text-sm: 0.875rem;\n    --text-sm--line-height: calc(1.25 / 0.875);\n    --text-base: 1rem;\n    --text-base--line-height: calc(1.5 / 1);\n    --text-lg: 1.125rem;\n    --text-lg--line-height: calc(1.75 / 1.125);\n    --text-xl: 1.25rem;\n    --text-xl--line-height: calc(1.75 / 1.25);\n    --text-2xl: 1.5rem;\n    --text-2xl--line-height: calc(2 / 1.5);\n    --text-3xl: 1.875rem;\n    --text-3xl--line-height: calc(2.25 / 1.875);\n    --text-4xl: 2.25rem;\n    --text-4xl--line-height: calc(2.5 / 2.25);\n    --text-5xl: 3rem;\n    --text-5xl--line-height: 1;\n    --text-6xl: 3.75rem;\n    --text-6xl--line-height: 1;\n    --text-7xl: 4.5rem;\n    --text-7xl--line-height: 1;\n    --font-weight-normal: 400;\n    --font-weight-medium: 500;\n    --font-weight-semibold: 600;\n    --font-weight-bold: 700;\n    --font-weight-extrabold: 800;\n    --tracking-tighter: -0.05em;\n    --tracking-tight: -0.025em;\n    --tracking-normal: 0em;\n    --tracking-wider: 0.05em;\n    --tracking-widest: 0.1em;\n    --leading-tight: 1.25;\n    --leading-relaxed: 1.625;\n    --radius-xs: 0.125rem;\n    --radius-2xl: 1rem;\n    --radius-3xl: 1.5rem;\n    --drop-shadow-lg: 0 4px 4px rgb(0 0 0 / 0.15);\n    --ease-in-out: cubic-bezier(0.4, 0, 0.2, 1);\n    --animate-pulse: pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;\n    --blur-sm: 8px;\n    --blur-md: 12px;\n    --blur-xl: 24px;\n    --blur-2xl: 40px;\n    --blur-3xl: 64px;\n    --aspect-video: 16 / 9;\n    --default-transition-duration: 150ms;\n    --default-transition-timing-function: cubic-bezier(0.4, 0, 0.2, 1);\n    --default-font-family: var(--font-geist-sans);\n    --default-mono-font-family: var(--font-geist-mono);\n    --color-background: var(--background);\n    --color-foreground: var(--foreground);\n    --color-border: var(--border);\n    --color-muted-foreground: var(--muted-foreground);\n    --color-primary: var(--primary);\n  }\n}\n@layer base {\n  *, ::after, ::before, ::backdrop, ::file-selector-button {\n    box-sizing: border-box;\n    margin: 0;\n    padding: 0;\n    border: 0 solid;\n  }\n  html, :host {\n    line-height: 1.5;\n    -webkit-text-size-adjust: 100%;\n    tab-size: 4;\n    font-family: var(--default-font-family, ui-sans-serif, system-ui, sans-serif, \"Apple Color Emoji\", \"Segoe UI Emoji\", \"Segoe UI Symbol\", \"Noto Color Emoji\");\n    font-feature-settings: var(--default-font-feature-settings, normal);\n    font-variation-settings: var(--default-font-variation-settings, normal);\n    -webkit-tap-highlight-color: transparent;\n  }\n  hr {\n    height: 0;\n    color: inherit;\n    border-top-width: 1px;\n  }\n  abbr:where([title]) {\n    -webkit-text-decoration: underline dotted;\n    text-decoration: underline dotted;\n  }\n  h1, h2, h3, h4, h5, h6 {\n    font-size: inherit;\n    font-weight: inherit;\n  }\n  a {\n    color: inherit;\n    -webkit-text-decoration: inherit;\n    text-decoration: inherit;\n  }\n  b, strong {\n    font-weight: bolder;\n  }\n  code, kbd, samp, pre {\n    font-family: var(--default-mono-font-family, ui-monospace, SFMono-Regular, Menlo, Monaco, Consolas, \"Liberation Mono\", \"Courier New\", monospace);\n    font-feature-settings: var(--default-mono-font-feature-settings, normal);\n    font-variation-settings: var(--default-mono-font-variation-settings, normal);\n    font-size: 1em;\n  }\n  small {\n    font-size: 80%;\n  }\n  sub, sup {\n    font-size: 75%;\n    line-height: 0;\n    position: relative;\n    vertical-align: baseline;\n  }\n  sub {\n    bottom: -0.25em;\n  }\n  sup {\n    top: -0.5em;\n  }\n  table {\n    text-indent: 0;\n    border-color: inherit;\n    border-collapse: collapse;\n  }\n  :-moz-focusring {\n    outline: auto;\n  }\n  progress {\n    vertical-align: baseline;\n  }\n  summary {\n    display: list-item;\n  }\n  ol, ul, menu {\n    list-style: none;\n  }\n  img, svg, video, canvas, audio, iframe, embed, object {\n    display: block;\n    vertical-align: middle;\n  }\n  img, video {\n    max-width: 100%;\n    height: auto;\n  }\n  button, input, select, optgroup, textarea, ::file-selector-button {\n    font: inherit;\n    font-feature-settings: inherit;\n    font-variation-settings: inherit;\n    letter-spacing: inherit;\n    color: inherit;\n    border-radius: 0;\n    background-color: transparent;\n    opacity: 1;\n  }\n  :where(select:is([multiple], [size])) optgroup {\n    font-weight: bolder;\n  }\n  :where(select:is([multiple], [size])) optgroup option {\n    padding-inline-start: 20px;\n  }\n  ::file-selector-button {\n    margin-inline-end: 4px;\n  }\n  ::placeholder {\n    opacity: 1;\n  }\n  @supports (not (-webkit-appearance: -apple-pay-button))  or (contain-intrinsic-size: 1px) {\n    ::placeholder {\n      color: currentcolor;\n      @supports (color: color-mix(in lab, red, red)) {\n        color: color-mix(in oklab, currentcolor 50%, transparent);\n      }\n    }\n  }\n  textarea {\n    resize: vertical;\n  }\n  ::-webkit-search-decoration {\n    -webkit-appearance: none;\n  }\n  ::-webkit-date-and-time-value {\n    min-height: 1lh;\n    text-align: inherit;\n  }\n  ::-webkit-datetime-edit {\n    display: inline-flex;\n  }\n  ::-webkit-datetime-edit-fields-wrapper {\n    padding: 0;\n  }\n  ::-webkit-datetime-edit, ::-webkit-datetime-edit-year-field, ::-webkit-datetime-edit-month-field, ::-webkit-datetime-edit-day-field, ::-webkit-datetime-edit-hour-field, ::-webkit-datetime-edit-minute-field, ::-webkit-datetime-edit-second-field, ::-webkit-datetime-edit-millisecond-field, ::-webkit-datetime-edit-meridiem-field {\n    padding-block: 0;\n  }\n  :-moz-ui-invalid {\n    box-shadow: none;\n  }\n  button, input:where([type=\"button\"], [type=\"reset\"], [type=\"submit\"]), ::file-selector-button {\n    appearance: button;\n  }\n  ::-webkit-inner-spin-button, ::-webkit-outer-spin-button {\n    height: auto;\n  }\n  [hidden]:where(:not([hidden=\"until-found\"])) {\n    display: none !important;\n  }\n}\n@layer utilities {\n  .\\@container\\/card-header {\n    container-type: inline-size;\n    container-name: card-header;\n  }\n  .pointer-events-none {\n    pointer-events: none;\n  }\n  .invisible {\n    visibility: hidden;\n  }\n  .visible {\n    visibility: visible;\n  }\n  .sr-only {\n    position: absolute;\n    width: 1px;\n    height: 1px;\n    padding: 0;\n    margin: -1px;\n    overflow: hidden;\n    clip: rect(0, 0, 0, 0);\n    white-space: nowrap;\n    border-width: 0;\n  }\n  .absolute {\n    position: absolute;\n  }\n  .fixed {\n    position: fixed;\n  }\n  .relative {\n    position: relative;\n  }\n  .static {\n    position: static;\n  }\n  .sticky {\n    position: sticky;\n  }\n  .-inset-4 {\n    inset: calc(var(--spacing) * -4);\n  }\n  .inset-0 {\n    inset: calc(var(--spacing) * 0);\n  }\n  .inset-x-0 {\n    inset-inline: calc(var(--spacing) * 0);\n  }\n  .inset-y-0 {\n    inset-block: calc(var(--spacing) * 0);\n  }\n  .-top-12 {\n    top: calc(var(--spacing) * -12);\n  }\n  .-top-20 {\n    top: calc(var(--spacing) * -20);\n  }\n  .-top-24 {\n    top: calc(var(--spacing) * -24);\n  }\n  .-top-32 {\n    top: calc(var(--spacing) * -32);\n  }\n  .-top-40 {\n    top: calc(var(--spacing) * -40);\n  }\n  .top-0 {\n    top: calc(var(--spacing) * 0);\n  }\n  .top-1\\.5 {\n    top: calc(var(--spacing) * 1.5);\n  }\n  .top-1\\/2 {\n    top: calc(1/2 * 100%);\n  }\n  .top-1\\/4 {\n    top: calc(1/4 * 100%);\n  }\n  .top-3\\.5 {\n    top: calc(var(--spacing) * 3.5);\n  }\n  .top-4 {\n    top: calc(var(--spacing) * 4);\n  }\n  .top-6 {\n    top: calc(var(--spacing) * 6);\n  }\n  .top-20 {\n    top: calc(var(--spacing) * 20);\n  }\n  .top-24 {\n    top: calc(var(--spacing) * 24);\n  }\n  .top-\\[1px\\] {\n    top: 1px;\n  }\n  .top-\\[50\\%\\] {\n    top: 50%;\n  }\n  .top-\\[60\\%\\] {\n    top: 60%;\n  }\n  .top-full {\n    top: 100%;\n  }\n  .-right-12 {\n    right: calc(var(--spacing) * -12);\n  }\n  .right-0 {\n    right: calc(var(--spacing) * 0);\n  }\n  .right-1 {\n    right: calc(var(--spacing) * 1);\n  }\n  .right-1\\/4 {\n    right: calc(1/4 * 100%);\n  }\n  .right-2 {\n    right: calc(var(--spacing) * 2);\n  }\n  .right-3 {\n    right: calc(var(--spacing) * 3);\n  }\n  .right-4 {\n    right: calc(var(--spacing) * 4);\n  }\n  .right-5 {\n    right: calc(var(--spacing) * 5);\n  }\n  .right-6 {\n    right: calc(var(--spacing) * 6);\n  }\n  .-bottom-12 {\n    bottom: calc(var(--spacing) * -12);\n  }\n  .-bottom-24 {\n    bottom: calc(var(--spacing) * -24);\n  }\n  .bottom-0 {\n    bottom: calc(var(--spacing) * 0);\n  }\n  .bottom-1\\/4 {\n    bottom: calc(1/4 * 100%);\n  }\n  .bottom-5 {\n    bottom: calc(var(--spacing) * 5);\n  }\n  .-left-12 {\n    left: calc(var(--spacing) * -12);\n  }\n  .left-0 {\n    left: calc(var(--spacing) * 0);\n  }\n  .left-1 {\n    left: calc(var(--spacing) * 1);\n  }\n  .left-1\\/2 {\n    left: calc(1/2 * 100%);\n  }\n  .left-1\\/4 {\n    left: calc(1/4 * 100%);\n  }\n  .left-2 {\n    left: calc(var(--spacing) * 2);\n  }\n  .left-\\[50\\%\\] {\n    left: 50%;\n  }\n  .isolate {\n    isolation: isolate;\n  }\n  .-z-10 {\n    z-index: calc(10 * -1);\n  }\n  .z-10 {\n    z-index: 10;\n  }\n  .z-20 {\n    z-index: 20;\n  }\n  .z-40 {\n    z-index: 40;\n  }\n  .z-50 {\n    z-index: 50;\n  }\n  .z-\\[1\\] {\n    z-index: 1;\n  }\n  .col-span-4 {\n    grid-column: span 4 / span 4;\n  }\n  .col-start-2 {\n    grid-column-start: 2;\n  }\n  .row-span-2 {\n    grid-row: span 2 / span 2;\n  }\n  .row-start-1 {\n    grid-row-start: 1;\n  }\n  .container {\n    width: 100%;\n    @media (width >= 40rem) {\n      max-width: 40rem;\n    }\n    @media (width >= 48rem) {\n      max-width: 48rem;\n    }\n    @media (width >= 64rem) {\n      max-width: 64rem;\n    }\n    @media (width >= 80rem) {\n      max-width: 80rem;\n    }\n    @media (width >= 96rem) {\n      max-width: 96rem;\n    }\n  }\n  .-mx-1 {\n    margin-inline: calc(var(--spacing) * -1);\n  }\n  .-mx-2 {\n    margin-inline: calc(var(--spacing) * -2);\n  }\n  .mx-2 {\n    margin-inline: calc(var(--spacing) * 2);\n  }\n  .mx-3\\.5 {\n    margin-inline: calc(var(--spacing) * 3.5);\n  }\n  .mx-auto {\n    margin-inline: auto;\n  }\n  .my-0\\.5 {\n    margin-block: calc(var(--spacing) * 0.5);\n  }\n  .my-1 {\n    margin-block: calc(var(--spacing) * 1);\n  }\n  .my-16 {\n    margin-block: calc(var(--spacing) * 16);\n  }\n  .prose {\n    color: var(--tw-prose-body);\n    max-width: 65ch;\n    :where(p):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 1.25em;\n      margin-bottom: 1.25em;\n    }\n    :where([class~=\"lead\"]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-lead);\n      font-size: 1.25em;\n      line-height: 1.6;\n      margin-top: 1.2em;\n      margin-bottom: 1.2em;\n    }\n    :where(a):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-links);\n      text-decoration: underline;\n      font-weight: 500;\n    }\n    :where(strong):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-bold);\n      font-weight: 600;\n    }\n    :where(a strong):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: inherit;\n    }\n    :where(blockquote strong):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: inherit;\n    }\n    :where(thead th strong):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: inherit;\n    }\n    :where(ol):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: decimal;\n      margin-top: 1.25em;\n      margin-bottom: 1.25em;\n      padding-inline-start: 1.625em;\n    }\n    :where(ol[type=\"A\"]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: upper-alpha;\n    }\n    :where(ol[type=\"a\"]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: lower-alpha;\n    }\n    :where(ol[type=\"A\" s]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: upper-alpha;\n    }\n    :where(ol[type=\"a\" s]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: lower-alpha;\n    }\n    :where(ol[type=\"I\"]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: upper-roman;\n    }\n    :where(ol[type=\"i\"]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: lower-roman;\n    }\n    :where(ol[type=\"I\" s]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: upper-roman;\n    }\n    :where(ol[type=\"i\" s]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: lower-roman;\n    }\n    :where(ol[type=\"1\"]):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: decimal;\n    }\n    :where(ul):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      list-style-type: disc;\n      margin-top: 1.25em;\n      margin-bottom: 1.25em;\n      padding-inline-start: 1.625em;\n    }\n    :where(ol > li):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *))::marker {\n      font-weight: 400;\n      color: var(--tw-prose-counters);\n    }\n    :where(ul > li):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *))::marker {\n      color: var(--tw-prose-bullets);\n    }\n    :where(dt):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-headings);\n      font-weight: 600;\n      margin-top: 1.25em;\n    }\n    :where(hr):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      border-color: var(--tw-prose-hr);\n      border-top-width: 1;\n      margin-top: 3em;\n      margin-bottom: 3em;\n    }\n    :where(blockquote):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      font-weight: 500;\n      font-style: italic;\n      color: var(--tw-prose-quotes);\n      border-inline-start-width: 0.25rem;\n      border-inline-start-color: var(--tw-prose-quote-borders);\n      quotes: \"\\201C\"\"\\201D\"\"\\2018\"\"\\2019\";\n      margin-top: 1.6em;\n      margin-bottom: 1.6em;\n      padding-inline-start: 1em;\n    }\n    :where(blockquote p:first-of-type):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *))::before {\n      content: open-quote;\n    }\n    :where(blockquote p:last-of-type):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *))::after {\n      content: close-quote;\n    }\n    :where(h1):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-headings);\n      font-weight: 800;\n      font-size: 2.25em;\n      margin-top: 0;\n      margin-bottom: 0.8888889em;\n      line-height: 1.1111111;\n    }\n    :where(h1 strong):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      font-weight: 900;\n      color: inherit;\n    }\n    :where(h2):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-headings);\n      font-weight: 700;\n      font-size: 1.5em;\n      margin-top: 2em;\n      margin-bottom: 1em;\n      line-height: 1.3333333;\n    }\n    :where(h2 strong):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      font-weight: 800;\n      color: inherit;\n    }\n    :where(h3):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-headings);\n      font-weight: 600;\n      font-size: 1.25em;\n      margin-top: 1.6em;\n      margin-bottom: 0.6em;\n      line-height: 1.6;\n    }\n    :where(h3 strong):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      font-weight: 700;\n      color: inherit;\n    }\n    :where(h4):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-headings);\n      font-weight: 600;\n      margin-top: 1.5em;\n      margin-bottom: 0.5em;\n      line-height: 1.5;\n    }\n    :where(h4 strong):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      font-weight: 700;\n      color: inherit;\n    }\n    :where(img):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 2em;\n      margin-bottom: 2em;\n    }\n    :where(picture):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      display: block;\n      margin-top: 2em;\n      margin-bottom: 2em;\n    }\n    :where(video):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 2em;\n      margin-bottom: 2em;\n    }\n    :where(kbd):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      font-weight: 500;\n      font-family: inherit;\n      color: var(--tw-prose-kbd);\n      box-shadow: 0 0 0 1px rgb(var(--tw-prose-kbd-shadows) / 10%), 0 3px 0 rgb(var(--tw-prose-kbd-shadows) / 10%);\n      font-size: 0.875em;\n      border-radius: 0.3125rem;\n      padding-top: 0.1875em;\n      padding-inline-end: 0.375em;\n      padding-bottom: 0.1875em;\n      padding-inline-start: 0.375em;\n    }\n    :where(code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-code);\n      font-weight: 600;\n      font-size: 0.875em;\n    }\n    :where(code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *))::before {\n      content: \"`\";\n    }\n    :where(code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *))::after {\n      content: \"`\";\n    }\n    :where(a code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: inherit;\n    }\n    :where(h1 code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: inherit;\n    }\n    :where(h2 code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: inherit;\n      font-size: 0.875em;\n    }\n    :where(h3 code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: inherit;\n      font-size: 0.9em;\n    }\n    :where(h4 code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: inherit;\n    }\n    :where(blockquote code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: inherit;\n    }\n    :where(thead th code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: inherit;\n    }\n    :where(pre):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-pre-code);\n      background-color: var(--tw-prose-pre-bg);\n      overflow-x: auto;\n      font-weight: 400;\n      font-size: 0.875em;\n      line-height: 1.7142857;\n      margin-top: 1.7142857em;\n      margin-bottom: 1.7142857em;\n      border-radius: 0.375rem;\n      padding-top: 0.8571429em;\n      padding-inline-end: 1.1428571em;\n      padding-bottom: 0.8571429em;\n      padding-inline-start: 1.1428571em;\n    }\n    :where(pre code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      background-color: transparent;\n      border-width: 0;\n      border-radius: 0;\n      padding: 0;\n      font-weight: inherit;\n      color: inherit;\n      font-size: inherit;\n      font-family: inherit;\n      line-height: inherit;\n    }\n    :where(pre code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *))::before {\n      content: none;\n    }\n    :where(pre code):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *))::after {\n      content: none;\n    }\n    :where(table):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      width: 100%;\n      table-layout: auto;\n      margin-top: 2em;\n      margin-bottom: 2em;\n      font-size: 0.875em;\n      line-height: 1.7142857;\n    }\n    :where(thead):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      border-bottom-width: 1px;\n      border-bottom-color: var(--tw-prose-th-borders);\n    }\n    :where(thead th):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-headings);\n      font-weight: 600;\n      vertical-align: bottom;\n      padding-inline-end: 0.5714286em;\n      padding-bottom: 0.5714286em;\n      padding-inline-start: 0.5714286em;\n    }\n    :where(tbody tr):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      border-bottom-width: 1px;\n      border-bottom-color: var(--tw-prose-td-borders);\n    }\n    :where(tbody tr:last-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      border-bottom-width: 0;\n    }\n    :where(tbody td):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      vertical-align: baseline;\n    }\n    :where(tfoot):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      border-top-width: 1px;\n      border-top-color: var(--tw-prose-th-borders);\n    }\n    :where(tfoot td):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      vertical-align: top;\n    }\n    :where(th, td):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      text-align: start;\n    }\n    :where(figure > *):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0;\n      margin-bottom: 0;\n    }\n    :where(figcaption):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      color: var(--tw-prose-captions);\n      font-size: 0.875em;\n      line-height: 1.4285714;\n      margin-top: 0.8571429em;\n    }\n    --tw-prose-body: oklch(37.3% 0.034 259.733);\n    --tw-prose-headings: oklch(21% 0.034 264.665);\n    --tw-prose-lead: oklch(44.6% 0.03 256.802);\n    --tw-prose-links: oklch(21% 0.034 264.665);\n    --tw-prose-bold: oklch(21% 0.034 264.665);\n    --tw-prose-counters: oklch(55.1% 0.027 264.364);\n    --tw-prose-bullets: oklch(87.2% 0.01 258.338);\n    --tw-prose-hr: oklch(92.8% 0.006 264.531);\n    --tw-prose-quotes: oklch(21% 0.034 264.665);\n    --tw-prose-quote-borders: oklch(92.8% 0.006 264.531);\n    --tw-prose-captions: oklch(55.1% 0.027 264.364);\n    --tw-prose-kbd: oklch(21% 0.034 264.665);\n    --tw-prose-kbd-shadows: NaN NaN NaN;\n    --tw-prose-code: oklch(21% 0.034 264.665);\n    --tw-prose-pre-code: oklch(92.8% 0.006 264.531);\n    --tw-prose-pre-bg: oklch(27.8% 0.033 256.848);\n    --tw-prose-th-borders: oklch(87.2% 0.01 258.338);\n    --tw-prose-td-borders: oklch(92.8% 0.006 264.531);\n    --tw-prose-invert-body: oklch(87.2% 0.01 258.338);\n    --tw-prose-invert-headings: #fff;\n    --tw-prose-invert-lead: oklch(70.7% 0.022 261.325);\n    --tw-prose-invert-links: #fff;\n    --tw-prose-invert-bold: #fff;\n    --tw-prose-invert-counters: oklch(70.7% 0.022 261.325);\n    --tw-prose-invert-bullets: oklch(44.6% 0.03 256.802);\n    --tw-prose-invert-hr: oklch(37.3% 0.034 259.733);\n    --tw-prose-invert-quotes: oklch(96.7% 0.003 264.542);\n    --tw-prose-invert-quote-borders: oklch(37.3% 0.034 259.733);\n    --tw-prose-invert-captions: oklch(70.7% 0.022 261.325);\n    --tw-prose-invert-kbd: #fff;\n    --tw-prose-invert-kbd-shadows: 255 255 255;\n    --tw-prose-invert-code: #fff;\n    --tw-prose-invert-pre-code: oklch(87.2% 0.01 258.338);\n    --tw-prose-invert-pre-bg: rgb(0 0 0 / 50%);\n    --tw-prose-invert-th-borders: oklch(44.6% 0.03 256.802);\n    --tw-prose-invert-td-borders: oklch(37.3% 0.034 259.733);\n    font-size: 1rem;\n    line-height: 1.75;\n    :where(picture > img):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0;\n      margin-bottom: 0;\n    }\n    :where(li):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0.5em;\n      margin-bottom: 0.5em;\n    }\n    :where(ol > li):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      padding-inline-start: 0.375em;\n    }\n    :where(ul > li):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      padding-inline-start: 0.375em;\n    }\n    :where(.prose > ul > li p):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0.75em;\n      margin-bottom: 0.75em;\n    }\n    :where(.prose > ul > li > p:first-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 1.25em;\n    }\n    :where(.prose > ul > li > p:last-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-bottom: 1.25em;\n    }\n    :where(.prose > ol > li > p:first-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 1.25em;\n    }\n    :where(.prose > ol > li > p:last-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-bottom: 1.25em;\n    }\n    :where(ul ul, ul ol, ol ul, ol ol):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0.75em;\n      margin-bottom: 0.75em;\n    }\n    :where(dl):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 1.25em;\n      margin-bottom: 1.25em;\n    }\n    :where(dd):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0.5em;\n      padding-inline-start: 1.625em;\n    }\n    :where(hr + *):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0;\n    }\n    :where(h2 + *):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0;\n    }\n    :where(h3 + *):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0;\n    }\n    :where(h4 + *):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0;\n    }\n    :where(thead th:first-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      padding-inline-start: 0;\n    }\n    :where(thead th:last-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      padding-inline-end: 0;\n    }\n    :where(tbody td, tfoot td):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      padding-top: 0.5714286em;\n      padding-inline-end: 0.5714286em;\n      padding-bottom: 0.5714286em;\n      padding-inline-start: 0.5714286em;\n    }\n    :where(tbody td:first-child, tfoot td:first-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      padding-inline-start: 0;\n    }\n    :where(tbody td:last-child, tfoot td:last-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      padding-inline-end: 0;\n    }\n    :where(figure):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 2em;\n      margin-bottom: 2em;\n    }\n    :where(.prose > :first-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-top: 0;\n    }\n    :where(.prose > :last-child):not(:where([class~=\"not-prose\"],[class~=\"not-prose\"] *)) {\n      margin-bottom: 0;\n    }\n  }\n  .-mt-4 {\n    margin-top: calc(var(--spacing) * -4);\n  }\n  .-mt-20 {\n    margin-top: calc(var(--spacing) * -20);\n  }\n  .mt-0\\.5 {\n    margin-top: calc(var(--spacing) * 0.5);\n  }\n  .mt-1 {\n    margin-top: calc(var(--spacing) * 1);\n  }\n  .mt-1\\.5 {\n    margin-top: calc(var(--spacing) * 1.5);\n  }\n  .mt-2 {\n    margin-top: calc(var(--spacing) * 2);\n  }\n  .mt-3 {\n    margin-top: calc(var(--spacing) * 3);\n  }\n  .mt-4 {\n    margin-top: calc(var(--spacing) * 4);\n  }\n  .mt-8 {\n    margin-top: calc(var(--spacing) * 8);\n  }\n  .mt-12 {\n    margin-top: calc(var(--spacing) * 12);\n  }\n  .mt-16 {\n    margin-top: calc(var(--spacing) * 16);\n  }\n  .mt-auto {\n    margin-top: auto;\n  }\n  .mr-2 {\n    margin-right: calc(var(--spacing) * 2);\n  }\n  .mb-2 {\n    margin-bottom: calc(var(--spacing) * 2);\n  }\n  .mb-3 {\n    margin-bottom: calc(var(--spacing) * 3);\n  }\n  .mb-4 {\n    margin-bottom: calc(var(--spacing) * 4);\n  }\n  .mb-6 {\n    margin-bottom: calc(var(--spacing) * 6);\n  }\n  .mb-8 {\n    margin-bottom: calc(var(--spacing) * 8);\n  }\n  .mb-10 {\n    margin-bottom: calc(var(--spacing) * 10);\n  }\n  .mb-12 {\n    margin-bottom: calc(var(--spacing) * 12);\n  }\n  .mb-16 {\n    margin-bottom: calc(var(--spacing) * 16);\n  }\n  .mb-20 {\n    margin-bottom: calc(var(--spacing) * 20);\n  }\n  .-ml-4 {\n    margin-left: calc(var(--spacing) * -4);\n  }\n  .ml-1 {\n    margin-left: calc(var(--spacing) * 1);\n  }\n  .ml-auto {\n    margin-left: auto;\n  }\n  .line-clamp-1 {\n    overflow: hidden;\n    display: -webkit-box;\n    -webkit-box-orient: vertical;\n    -webkit-line-clamp: 1;\n  }\n  .block {\n    display: block;\n  }\n  .flex {\n    display: flex;\n  }\n  .grid {\n    display: grid;\n  }\n  .hidden {\n    display: none;\n  }\n  .inline-block {\n    display: inline-block;\n  }\n  .inline-flex {\n    display: inline-flex;\n  }\n  .table {\n    display: table;\n  }\n  .table-caption {\n    display: table-caption;\n  }\n  .table-cell {\n    display: table-cell;\n  }\n  .table-row {\n    display: table-row;\n  }\n  .field-sizing-content {\n    field-sizing: content;\n  }\n  .aspect-square {\n    aspect-ratio: 1 / 1;\n  }\n  .aspect-video {\n    aspect-ratio: var(--aspect-video);\n  }\n  .size-2 {\n    width: calc(var(--spacing) * 2);\n    height: calc(var(--spacing) * 2);\n  }\n  .size-2\\.5 {\n    width: calc(var(--spacing) * 2.5);\n    height: calc(var(--spacing) * 2.5);\n  }\n  .size-3 {\n    width: calc(var(--spacing) * 3);\n    height: calc(var(--spacing) * 3);\n  }\n  .size-3\\.5 {\n    width: calc(var(--spacing) * 3.5);\n    height: calc(var(--spacing) * 3.5);\n  }\n  .size-4 {\n    width: calc(var(--spacing) * 4);\n    height: calc(var(--spacing) * 4);\n  }\n  .size-7 {\n    width: calc(var(--spacing) * 7);\n    height: calc(var(--spacing) * 7);\n  }\n  .size-8 {\n    width: calc(var(--spacing) * 8);\n    height: calc(var(--spacing) * 8);\n  }\n  .size-9 {\n    width: calc(var(--spacing) * 9);\n    height: calc(var(--spacing) * 9);\n  }\n  .size-full {\n    width: 100%;\n    height: 100%;\n  }\n  .h-1\\.5 {\n    height: calc(var(--spacing) * 1.5);\n  }\n  .h-2 {\n    height: calc(var(--spacing) * 2);\n  }\n  .h-2\\.5 {\n    height: calc(var(--spacing) * 2.5);\n  }\n  .h-3 {\n    height: calc(var(--spacing) * 3);\n  }\n  .h-4 {\n    height: calc(var(--spacing) * 4);\n  }\n  .h-5 {\n    height: calc(var(--spacing) * 5);\n  }\n  .h-6 {\n    height: calc(var(--spacing) * 6);\n  }\n  .h-7 {\n    height: calc(var(--spacing) * 7);\n  }\n  .h-8 {\n    height: calc(var(--spacing) * 8);\n  }\n  .h-9 {\n    height: calc(var(--spacing) * 9);\n  }\n  .h-10 {\n    height: calc(var(--spacing) * 10);\n  }\n  .h-12 {\n    height: calc(var(--spacing) * 12);\n  }\n  .h-48 {\n    height: calc(var(--spacing) * 48);\n  }\n  .h-64 {\n    height: calc(var(--spacing) * 64);\n  }\n  .h-80 {\n    height: calc(var(--spacing) * 80);\n  }\n  .h-96 {\n    height: calc(var(--spacing) * 96);\n  }\n  .h-\\[1\\.2rem\\] {\n    height: 1.2rem;\n  }\n  .h-\\[1\\.15rem\\] {\n    height: 1.15rem;\n  }\n  .h-\\[calc\\(100\\%-1px\\)\\] {\n    height: calc(100% - 1px);\n  }\n  .h-\\[var\\(--radix-navigation-menu-viewport-height\\)\\] {\n    height: var(--radix-navigation-menu-viewport-height);\n  }\n  .h-\\[var\\(--radix-select-trigger-height\\)\\] {\n    height: var(--radix-select-trigger-height);\n  }\n  .h-auto {\n    height: auto;\n  }\n  .h-full {\n    height: 100%;\n  }\n  .h-px {\n    height: 1px;\n  }\n  .h-svh {\n    height: 100svh;\n  }\n  .max-h-\\(--radix-context-menu-content-available-height\\) {\n    max-height: var(--radix-context-menu-content-available-height);\n  }\n  .max-h-\\(--radix-dropdown-menu-content-available-height\\) {\n    max-height: var(--radix-dropdown-menu-content-available-height);\n  }\n  .max-h-\\(--radix-select-content-available-height\\) {\n    max-height: var(--radix-select-content-available-height);\n  }\n  .max-h-\\[50vh\\] {\n    max-height: 50vh;\n  }\n  .max-h-\\[90vh\\] {\n    max-height: 90vh;\n  }\n  .max-h-\\[300px\\] {\n    max-height: 300px;\n  }\n  .min-h-0 {\n    min-height: calc(var(--spacing) * 0);\n  }\n  .min-h-4 {\n    min-height: calc(var(--spacing) * 4);\n  }\n  .min-h-16 {\n    min-height: calc(var(--spacing) * 16);\n  }\n  .min-h-20 {\n    min-height: calc(var(--spacing) * 20);\n  }\n  .min-h-screen {\n    min-height: 100vh;\n  }\n  .min-h-svh {\n    min-height: 100svh;\n  }\n  .\\!w-\\[450px\\] {\n    width: 450px !important;\n  }\n  .w-\\(--sidebar-width\\) {\n    width: var(--sidebar-width);\n  }\n  .w-0 {\n    width: calc(var(--spacing) * 0);\n  }\n  .w-1 {\n    width: calc(var(--spacing) * 1);\n  }\n  .w-2 {\n    width: calc(var(--spacing) * 2);\n  }\n  .w-2\\.5 {\n    width: calc(var(--spacing) * 2.5);\n  }\n  .w-3 {\n    width: calc(var(--spacing) * 3);\n  }\n  .w-3\\/4 {\n    width: calc(3/4 * 100%);\n  }\n  .w-4 {\n    width: calc(var(--spacing) * 4);\n  }\n  .w-5 {\n    width: calc(var(--spacing) * 5);\n  }\n  .w-6 {\n    width: calc(var(--spacing) * 6);\n  }\n  .w-7 {\n    width: calc(var(--spacing) * 7);\n  }\n  .w-8 {\n    width: calc(var(--spacing) * 8);\n  }\n  .w-9 {\n    width: calc(var(--spacing) * 9);\n  }\n  .w-10 {\n    width: calc(var(--spacing) * 10);\n  }\n  .w-12 {\n    width: calc(var(--spacing) * 12);\n  }\n  .w-16 {\n    width: calc(var(--spacing) * 16);\n  }\n  .w-56 {\n    width: calc(var(--spacing) * 56);\n  }\n  .w-64 {\n    width: calc(var(--spacing) * 64);\n  }\n  .w-72 {\n    width: calc(var(--spacing) * 72);\n  }\n  .w-80 {\n    width: calc(var(--spacing) * 80);\n  }\n  .w-96 {\n    width: calc(var(--spacing) * 96);\n  }\n  .w-\\[1\\.2rem\\] {\n    width: 1.2rem;\n  }\n  .w-\\[100px\\] {\n    width: 100px;\n  }\n  .w-auto {\n    width: auto;\n  }\n  .w-fit {\n    width: fit-content;\n  }\n  .w-full {\n    width: 100%;\n  }\n  .w-max {\n    width: max-content;\n  }\n  .w-px {\n    width: 1px;\n  }\n  .max-w-\\(--skeleton-width\\) {\n    max-width: var(--skeleton-width);\n  }\n  .max-w-2xl {\n    max-width: var(--container-2xl);\n  }\n  .max-w-3xl {\n    max-width: var(--container-3xl);\n  }\n  .max-w-4xl {\n    max-width: var(--container-4xl);\n  }\n  .max-w-5xl {\n    max-width: var(--container-5xl);\n  }\n  .max-w-6xl {\n    max-width: var(--container-6xl);\n  }\n  .max-w-7xl {\n    max-width: var(--container-7xl);\n  }\n  .max-w-\\[calc\\(100\\%-2rem\\)\\] {\n    max-width: calc(100% - 2rem);\n  }\n  .max-w-lg {\n    max-width: var(--container-lg);\n  }\n  .max-w-max {\n    max-width: max-content;\n  }\n  .max-w-md {\n    max-width: var(--container-md);\n  }\n  .max-w-none {\n    max-width: none;\n  }\n  .max-w-prose {\n    max-width: 65ch;\n  }\n  .max-w-sm {\n    max-width: var(--container-sm);\n  }\n  .max-w-xl {\n    max-width: var(--container-xl);\n  }\n  .max-w-xs {\n    max-width: var(--container-xs);\n  }\n  .min-w-0 {\n    min-width: calc(var(--spacing) * 0);\n  }\n  .min-w-5 {\n    min-width: calc(var(--spacing) * 5);\n  }\n  .min-w-8 {\n    min-width: calc(var(--spacing) * 8);\n  }\n  .min-w-9 {\n    min-width: calc(var(--spacing) * 9);\n  }\n  .min-w-10 {\n    min-width: calc(var(--spacing) * 10);\n  }\n  .min-w-\\[8rem\\] {\n    min-width: 8rem;\n  }\n  .min-w-\\[12rem\\] {\n    min-width: 12rem;\n  }\n  .min-w-\\[var\\(--radix-select-trigger-width\\)\\] {\n    min-width: var(--radix-select-trigger-width);\n  }\n  .flex-1 {\n    flex: 1;\n  }\n  .flex-shrink-0 {\n    flex-shrink: 0;\n  }\n  .shrink {\n    flex-shrink: 1;\n  }\n  .shrink-0 {\n    flex-shrink: 0;\n  }\n  .flex-grow {\n    flex-grow: 1;\n  }\n  .grow {\n    flex-grow: 1;\n  }\n  .grow-0 {\n    flex-grow: 0;\n  }\n  .basis-1\\/4 {\n    flex-basis: calc(1/4 * 100%);\n  }\n  .basis-full {\n    flex-basis: 100%;\n  }\n  .caption-bottom {\n    caption-side: bottom;\n  }\n  .border-collapse {\n    border-collapse: collapse;\n  }\n  .origin-\\(--radix-context-menu-content-transform-origin\\) {\n    transform-origin: var(--radix-context-menu-content-transform-origin);\n  }\n  .origin-\\(--radix-dropdown-menu-content-transform-origin\\) {\n    transform-origin: var(--radix-dropdown-menu-content-transform-origin);\n  }\n  .origin-\\(--radix-hover-card-content-transform-origin\\) {\n    transform-origin: var(--radix-hover-card-content-transform-origin);\n  }\n  .origin-\\(--radix-menubar-content-transform-origin\\) {\n    transform-origin: var(--radix-menubar-content-transform-origin);\n  }\n  .origin-\\(--radix-popover-content-transform-origin\\) {\n    transform-origin: var(--radix-popover-content-transform-origin);\n  }\n  .origin-\\(--radix-select-content-transform-origin\\) {\n    transform-origin: var(--radix-select-content-transform-origin);\n  }\n  .origin-\\(--radix-tooltip-content-transform-origin\\) {\n    transform-origin: var(--radix-tooltip-content-transform-origin);\n  }\n  .-translate-x-1\\/2 {\n    --tw-translate-x: calc(calc(1/2 * 100%) * -1);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .-translate-x-px {\n    --tw-translate-x: -1px;\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-x-\\[-50\\%\\] {\n    --tw-translate-x: -50%;\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-x-px {\n    --tw-translate-x: 1px;\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .-translate-y-1\\/2 {\n    --tw-translate-y: calc(calc(1/2 * 100%) * -1);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .-translate-y-2 {\n    --tw-translate-y: calc(var(--spacing) * -2);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-y-0 {\n    --tw-translate-y: calc(var(--spacing) * 0);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-y-0\\.5 {\n    --tw-translate-y: calc(var(--spacing) * 0.5);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-y-\\[-50\\%\\] {\n    --tw-translate-y: -50%;\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .translate-y-\\[calc\\(-50\\%_-_2px\\)\\] {\n    --tw-translate-y: calc(-50% - 2px);\n    translate: var(--tw-translate-x) var(--tw-translate-y);\n  }\n  .scale-0 {\n    --tw-scale-x: 0%;\n    --tw-scale-y: 0%;\n    --tw-scale-z: 0%;\n    scale: var(--tw-scale-x) var(--tw-scale-y);\n  }\n  .scale-100 {\n    --tw-scale-x: 100%;\n    --tw-scale-y: 100%;\n    --tw-scale-z: 100%;\n    scale: var(--tw-scale-x) var(--tw-scale-y);\n  }\n  .scale-125 {\n    --tw-scale-x: 125%;\n    --tw-scale-y: 125%;\n    --tw-scale-z: 125%;\n    scale: var(--tw-scale-x) var(--tw-scale-y);\n  }\n  .scale-150 {\n    --tw-scale-x: 150%;\n    --tw-scale-y: 150%;\n    --tw-scale-z: 150%;\n    scale: var(--tw-scale-x) var(--tw-scale-y);\n  }\n  .rotate-0 {\n    rotate: 0deg;\n  }\n  .rotate-45 {\n    rotate: 45deg;\n  }\n  .rotate-90 {\n    rotate: 90deg;\n  }\n  .transform {\n    transform: var(--tw-rotate-x,) var(--tw-rotate-y,) var(--tw-rotate-z,) var(--tw-skew-x,) var(--tw-skew-y,);\n  }\n  .animate-caret-blink {\n    animation: caret-blink 1.25s ease-out infinite;\n  }\n  .animate-in {\n    animation: enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease);\n  }\n  .animate-pulse {\n    animation: var(--animate-pulse);\n  }\n  .cursor-default {\n    cursor: default;\n  }\n  .cursor-pointer {\n    cursor: pointer;\n  }\n  .touch-manipulation {\n    touch-action: manipulation;\n  }\n  .touch-none {\n    touch-action: none;\n  }\n  .scroll-m-20 {\n    scroll-margin: calc(var(--spacing) * 20);\n  }\n  .scroll-my-1 {\n    scroll-margin-block: calc(var(--spacing) * 1);\n  }\n  .scroll-py-1 {\n    scroll-padding-block: calc(var(--spacing) * 1);\n  }\n  .list-none {\n    list-style-type: none;\n  }\n  .auto-rows-min {\n    grid-auto-rows: min-content;\n  }\n  .grid-cols-1 {\n    grid-template-columns: repeat(1, minmax(0, 1fr));\n  }\n  .grid-cols-2 {\n    grid-template-columns: repeat(2, minmax(0, 1fr));\n  }\n  .grid-cols-\\[0_1fr\\] {\n    grid-template-columns: 0 1fr;\n  }\n  .grid-rows-\\[auto_auto\\] {\n    grid-template-rows: auto auto;\n  }\n  .flex-col {\n    flex-direction: column;\n  }\n  .flex-col-reverse {\n    flex-direction: column-reverse;\n  }\n  .flex-row {\n    flex-direction: row;\n  }\n  .flex-wrap {\n    flex-wrap: wrap;\n  }\n  .items-baseline {\n    align-items: baseline;\n  }\n  .items-center {\n    align-items: center;\n  }\n  .items-end {\n    align-items: flex-end;\n  }\n  .items-start {\n    align-items: flex-start;\n  }\n  .items-stretch {\n    align-items: stretch;\n  }\n  .justify-between {\n    justify-content: space-between;\n  }\n  .justify-center {\n    justify-content: center;\n  }\n  .justify-end {\n    justify-content: flex-end;\n  }\n  .justify-start {\n    justify-content: flex-start;\n  }\n  .justify-items-start {\n    justify-items: start;\n  }\n  .gap-0 {\n    gap: calc(var(--spacing) * 0);\n  }\n  .gap-1 {\n    gap: calc(var(--spacing) * 1);\n  }\n  .gap-1\\.5 {\n    gap: calc(var(--spacing) * 1.5);\n  }\n  .gap-2 {\n    gap: calc(var(--spacing) * 2);\n  }\n  .gap-3 {\n    gap: calc(var(--spacing) * 3);\n  }\n  .gap-4 {\n    gap: calc(var(--spacing) * 4);\n  }\n  .gap-6 {\n    gap: calc(var(--spacing) * 6);\n  }\n  .gap-8 {\n    gap: calc(var(--spacing) * 8);\n  }\n  .gap-10 {\n    gap: calc(var(--spacing) * 10);\n  }\n  .gap-12 {\n    gap: calc(var(--spacing) * 12);\n  }\n  .gap-14 {\n    gap: calc(var(--spacing) * 14);\n  }\n  .gap-24 {\n    gap: calc(var(--spacing) * 24);\n  }\n  .space-y-1 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 1) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-2 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 2) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 2) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-4 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 4) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 4) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-6 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 6) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 6) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-8 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 8) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 8) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-y-12 {\n    :where(& > :not(:last-child)) {\n      --tw-space-y-reverse: 0;\n      margin-block-start: calc(calc(var(--spacing) * 12) * var(--tw-space-y-reverse));\n      margin-block-end: calc(calc(var(--spacing) * 12) * calc(1 - var(--tw-space-y-reverse)));\n    }\n  }\n  .space-x-1 {\n    :where(& > :not(:last-child)) {\n      --tw-space-x-reverse: 0;\n      margin-inline-start: calc(calc(var(--spacing) * 1) * var(--tw-space-x-reverse));\n      margin-inline-end: calc(calc(var(--spacing) * 1) * calc(1 - var(--tw-space-x-reverse)));\n    }\n  }\n  .gap-y-0\\.5 {\n    row-gap: calc(var(--spacing) * 0.5);\n  }\n  .self-start {\n    align-self: flex-start;\n  }\n  .justify-self-end {\n    justify-self: flex-end;\n  }\n  .overflow-auto {\n    overflow: auto;\n  }\n  .overflow-hidden {\n    overflow: hidden;\n  }\n  .overflow-x-auto {\n    overflow-x: auto;\n  }\n  .overflow-x-hidden {\n    overflow-x: hidden;\n  }\n  .overflow-y-auto {\n    overflow-y: auto;\n  }\n  .scroll-smooth {\n    scroll-behavior: smooth;\n  }\n  .rounded {\n    border-radius: 0.25rem;\n  }\n  .rounded-2xl {\n    border-radius: var(--radius-2xl);\n  }\n  .rounded-3xl {\n    border-radius: var(--radius-3xl);\n  }\n  .rounded-\\[2px\\] {\n    border-radius: 2px;\n  }\n  .rounded-\\[4px\\] {\n    border-radius: 4px;\n  }\n  .rounded-\\[inherit\\] {\n    border-radius: inherit;\n  }\n  .rounded-full {\n    border-radius: calc(infinity * 1px);\n  }\n  .rounded-lg {\n    border-radius: var(--radius);\n  }\n  .rounded-md {\n    border-radius: calc(var(--radius) - 2px);\n  }\n  .rounded-none {\n    border-radius: 0;\n  }\n  .rounded-sm {\n    border-radius: calc(var(--radius) - 4px);\n  }\n  .rounded-xl {\n    border-radius: calc(var(--radius) + 4px);\n  }\n  .rounded-xs {\n    border-radius: var(--radius-xs);\n  }\n  .rounded-t-xl {\n    border-top-left-radius: calc(var(--radius) + 4px);\n    border-top-right-radius: calc(var(--radius) + 4px);\n  }\n  .rounded-tl-sm {\n    border-top-left-radius: calc(var(--radius) - 4px);\n  }\n  .border {\n    border-style: var(--tw-border-style);\n    border-width: 1px;\n  }\n  .border-0 {\n    border-style: var(--tw-border-style);\n    border-width: 0px;\n  }\n  .border-\\[1\\.5px\\] {\n    border-style: var(--tw-border-style);\n    border-width: 1.5px;\n  }\n  .border-y {\n    border-block-style: var(--tw-border-style);\n    border-block-width: 1px;\n  }\n  .border-t {\n    border-top-style: var(--tw-border-style);\n    border-top-width: 1px;\n  }\n  .border-r {\n    border-right-style: var(--tw-border-style);\n    border-right-width: 1px;\n  }\n  .border-b {\n    border-bottom-style: var(--tw-border-style);\n    border-bottom-width: 1px;\n  }\n  .border-l {\n    border-left-style: var(--tw-border-style);\n    border-left-width: 1px;\n  }\n  .border-dashed {\n    --tw-border-style: dashed;\n    border-style: dashed;\n  }\n  .border-\\(--color-border\\) {\n    border-color: var(--color-border);\n  }\n  .border-border {\n    border-color: var(--border);\n  }\n  .border-border\\/50 {\n    border-color: var(--border);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--border) 50%, transparent);\n    }\n  }\n  .border-foreground\\/10 {\n    border-color: var(--foreground);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--foreground) 10%, transparent);\n    }\n  }\n  .border-gray-600\\/20 {\n    border-color: color-mix(in srgb, oklch(44.6% 0.03 256.802) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-gray-600) 20%, transparent);\n    }\n  }\n  .border-gray-700\\/30 {\n    border-color: color-mix(in srgb, oklch(37.3% 0.034 259.733) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-gray-700) 30%, transparent);\n    }\n  }\n  .border-input {\n    border-color: var(--input);\n  }\n  .border-neutral-600 {\n    border-color: var(--color-neutral-600);\n  }\n  .border-neutral-600\\/40 {\n    border-color: color-mix(in srgb, oklch(43.9% 0 0) 40%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-neutral-600) 40%, transparent);\n    }\n  }\n  .border-neutral-700 {\n    border-color: var(--color-neutral-700);\n  }\n  .border-neutral-700\\/30 {\n    border-color: color-mix(in srgb, oklch(37.1% 0 0) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-neutral-700) 30%, transparent);\n    }\n  }\n  .border-orange-500\\/20 {\n    border-color: color-mix(in srgb, oklch(70.5% 0.213 47.604) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-orange-500) 20%, transparent);\n    }\n  }\n  .border-orange-500\\/30 {\n    border-color: color-mix(in srgb, oklch(70.5% 0.213 47.604) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-orange-500) 30%, transparent);\n    }\n  }\n  .border-primary {\n    border-color: var(--primary);\n  }\n  .border-sidebar-border {\n    border-color: var(--sidebar-border);\n  }\n  .border-transparent {\n    border-color: transparent;\n  }\n  .border-white\\/10 {\n    border-color: color-mix(in srgb, #fff 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-white) 10%, transparent);\n    }\n  }\n  .border-white\\/20 {\n    border-color: color-mix(in srgb, #fff 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      border-color: color-mix(in oklab, var(--color-white) 20%, transparent);\n    }\n  }\n  .border-t-transparent {\n    border-top-color: transparent;\n  }\n  .border-l-transparent {\n    border-left-color: transparent;\n  }\n  .bg-\\(--color-bg\\) {\n    background-color: var(--color-bg);\n  }\n  .bg-\\[\\#0a0a0a\\] {\n    background-color: #0a0a0a;\n  }\n  .bg-\\[\\#1a1a1a\\] {\n    background-color: #1a1a1a;\n  }\n  .bg-accent {\n    background-color: var(--accent);\n  }\n  .bg-background {\n    background-color: var(--background);\n  }\n  .bg-background\\/20 {\n    background-color: var(--background);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--background) 20%, transparent);\n    }\n  }\n  .bg-background\\/50 {\n    background-color: var(--background);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--background) 50%, transparent);\n    }\n  }\n  .bg-black {\n    background-color: var(--color-black);\n  }\n  .bg-black\\/20 {\n    background-color: color-mix(in srgb, #000 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-black) 20%, transparent);\n    }\n  }\n  .bg-black\\/50 {\n    background-color: color-mix(in srgb, #000 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-black) 50%, transparent);\n    }\n  }\n  .bg-black\\/80 {\n    background-color: color-mix(in srgb, #000 80%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-black) 80%, transparent);\n    }\n  }\n  .bg-border {\n    background-color: var(--border);\n  }\n  .bg-card {\n    background-color: var(--card);\n  }\n  .bg-destructive {\n    background-color: var(--destructive);\n  }\n  .bg-foreground {\n    background-color: var(--foreground);\n  }\n  .bg-gray-200 {\n    background-color: var(--color-gray-200);\n  }\n  .bg-gray-600 {\n    background-color: var(--color-gray-600);\n  }\n  .bg-gray-800\\/60 {\n    background-color: color-mix(in srgb, oklch(27.8% 0.033 256.848) 60%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-gray-800) 60%, transparent);\n    }\n  }\n  .bg-gray-900\\/30 {\n    background-color: color-mix(in srgb, oklch(21% 0.034 264.665) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-gray-900) 30%, transparent);\n    }\n  }\n  .bg-muted {\n    background-color: var(--muted);\n  }\n  .bg-muted\\/50 {\n    background-color: var(--muted);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--muted) 50%, transparent);\n    }\n  }\n  .bg-neutral-700\\/50 {\n    background-color: color-mix(in srgb, oklch(37.1% 0 0) 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-neutral-700) 50%, transparent);\n    }\n  }\n  .bg-neutral-800\\/50 {\n    background-color: color-mix(in srgb, oklch(26.9% 0 0) 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-neutral-800) 50%, transparent);\n    }\n  }\n  .bg-neutral-800\\/70 {\n    background-color: color-mix(in srgb, oklch(26.9% 0 0) 70%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-neutral-800) 70%, transparent);\n    }\n  }\n  .bg-neutral-900\\/20 {\n    background-color: color-mix(in srgb, oklch(20.5% 0 0) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-neutral-900) 20%, transparent);\n    }\n  }\n  .bg-neutral-900\\/30 {\n    background-color: color-mix(in srgb, oklch(20.5% 0 0) 30%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-neutral-900) 30%, transparent);\n    }\n  }\n  .bg-neutral-900\\/50 {\n    background-color: color-mix(in srgb, oklch(20.5% 0 0) 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-neutral-900) 50%, transparent);\n    }\n  }\n  .bg-orange-400\\/3 {\n    background-color: color-mix(in srgb, oklch(75% 0.183 55.934) 3%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-orange-400) 3%, transparent);\n    }\n  }\n  .bg-orange-500 {\n    background-color: var(--color-orange-500);\n  }\n  .bg-orange-500\\/5 {\n    background-color: color-mix(in srgb, oklch(70.5% 0.213 47.604) 5%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-orange-500) 5%, transparent);\n    }\n  }\n  .bg-orange-500\\/10 {\n    background-color: color-mix(in srgb, oklch(70.5% 0.213 47.604) 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-orange-500) 10%, transparent);\n    }\n  }\n  .bg-orange-600\\/8 {\n    background-color: color-mix(in srgb, oklch(64.6% 0.222 41.116) 8%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--color-orange-600) 8%, transparent);\n    }\n  }\n  .bg-popover {\n    background-color: var(--popover);\n  }\n  .bg-primary {\n    background-color: var(--primary);\n  }\n  .bg-primary\\/20 {\n    background-color: var(--primary);\n    @supports (color: color-mix(in lab, red, red)) {\n      background-color: color-mix(in oklab, var(--primary) 20%, transparent);\n    }\n  }\n  .bg-secondary {\n    background-color: var(--secondary);\n  }\n  .bg-sidebar {\n    background-color: var(--sidebar);\n  }\n  .bg-sidebar-border {\n    background-color: var(--sidebar-border);\n  }\n  .bg-transparent {\n    background-color: transparent;\n  }\n  .bg-gradient-to-b {\n    --tw-gradient-position: to bottom in oklab;\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .bg-gradient-to-bl {\n    --tw-gradient-position: to bottom left in oklab;\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .bg-gradient-to-br {\n    --tw-gradient-position: to bottom right in oklab;\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .bg-gradient-to-l {\n    --tw-gradient-position: to left in oklab;\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .bg-gradient-to-r {\n    --tw-gradient-position: to right in oklab;\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .bg-gradient-to-t {\n    --tw-gradient-position: to top in oklab;\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .bg-gradient-to-tr {\n    --tw-gradient-position: to top right in oklab;\n    background-image: linear-gradient(var(--tw-gradient-stops));\n  }\n  .from-\\[\\#1a1a1a\\] {\n    --tw-gradient-from: #1a1a1a;\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-background {\n    --tw-gradient-from: var(--background);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-blue-500\\/20 {\n    --tw-gradient-from: color-mix(in srgb, oklch(62.3% 0.214 259.815) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-blue-500) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-neutral-800\\/70 {\n    --tw-gradient-from: color-mix(in srgb, oklch(26.9% 0 0) 70%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-neutral-800) 70%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-orange-400\\/10 {\n    --tw-gradient-from: color-mix(in srgb, oklch(75% 0.183 55.934) 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-orange-400) 10%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-orange-400\\/15 {\n    --tw-gradient-from: color-mix(in srgb, oklch(75% 0.183 55.934) 15%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-orange-400) 15%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-orange-500\\/5 {\n    --tw-gradient-from: color-mix(in srgb, oklch(70.5% 0.213 47.604) 5%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-orange-500) 5%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-orange-500\\/10 {\n    --tw-gradient-from: color-mix(in srgb, oklch(70.5% 0.213 47.604) 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-orange-500) 10%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-orange-500\\/15 {\n    --tw-gradient-from: color-mix(in srgb, oklch(70.5% 0.213 47.604) 15%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-orange-500) 15%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-orange-500\\/20 {\n    --tw-gradient-from: color-mix(in srgb, oklch(70.5% 0.213 47.604) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-orange-500) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-orange-600 {\n    --tw-gradient-from: var(--color-orange-600);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-orange-600\\/10 {\n    --tw-gradient-from: color-mix(in srgb, oklch(64.6% 0.222 41.116) 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-orange-600) 10%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-orange-950\\/10 {\n    --tw-gradient-from: color-mix(in srgb, oklch(26.6% 0.079 36.259) 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-orange-950) 10%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-transparent {\n    --tw-gradient-from: transparent;\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .from-white\\/8 {\n    --tw-gradient-from: color-mix(in srgb, #fff 8%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-from: color-mix(in oklab, var(--color-white) 8%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .via-background {\n    --tw-gradient-via: var(--background);\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .via-orange-400\\/10 {\n    --tw-gradient-via: color-mix(in srgb, oklch(75% 0.183 55.934) 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-via: color-mix(in oklab, var(--color-orange-400) 10%, transparent);\n    }\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .via-orange-500\\/5 {\n    --tw-gradient-via: color-mix(in srgb, oklch(70.5% 0.213 47.604) 5%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-via: color-mix(in oklab, var(--color-orange-500) 5%, transparent);\n    }\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .via-orange-500\\/8 {\n    --tw-gradient-via: color-mix(in srgb, oklch(70.5% 0.213 47.604) 8%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-via: color-mix(in oklab, var(--color-orange-500) 8%, transparent);\n    }\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .via-purple-500\\/20 {\n    --tw-gradient-via: color-mix(in srgb, oklch(62.7% 0.265 303.9) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-via: color-mix(in oklab, var(--color-purple-500) 20%, transparent);\n    }\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .via-transparent {\n    --tw-gradient-via: transparent;\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .via-white\\/8 {\n    --tw-gradient-via: color-mix(in srgb, #fff 8%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-via: color-mix(in oklab, var(--color-white) 8%, transparent);\n    }\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .via-white\\/11 {\n    --tw-gradient-via: color-mix(in srgb, #fff 11%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-via: color-mix(in oklab, var(--color-white) 11%, transparent);\n    }\n    --tw-gradient-via-stops: var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-via) var(--tw-gradient-via-position), var(--tw-gradient-to) var(--tw-gradient-to-position);\n    --tw-gradient-stops: var(--tw-gradient-via-stops);\n  }\n  .to-black\\/50 {\n    --tw-gradient-to: color-mix(in srgb, #000 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-black) 50%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-orange-600\\/3 {\n    --tw-gradient-to: color-mix(in srgb, oklch(64.6% 0.222 41.116) 3%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-orange-600) 3%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-orange-600\\/5 {\n    --tw-gradient-to: color-mix(in srgb, oklch(64.6% 0.222 41.116) 5%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-orange-600) 5%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-orange-600\\/10 {\n    --tw-gradient-to: color-mix(in srgb, oklch(64.6% 0.222 41.116) 10%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-orange-600) 10%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-orange-700 {\n    --tw-gradient-to: var(--color-orange-700);\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-pink-500\\/20 {\n    --tw-gradient-to: color-mix(in srgb, oklch(65.6% 0.241 354.308) 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-gradient-to: color-mix(in oklab, var(--color-pink-500) 20%, transparent);\n    }\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .to-transparent {\n    --tw-gradient-to: transparent;\n    --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n  }\n  .fill-current {\n    fill: currentcolor;\n  }\n  .fill-primary {\n    fill: var(--primary);\n  }\n  .fill-white {\n    fill: var(--color-white);\n  }\n  .stroke-1 {\n    stroke-width: 1;\n  }\n  .object-cover {\n    object-fit: cover;\n  }\n  .p-0 {\n    padding: calc(var(--spacing) * 0);\n  }\n  .p-1 {\n    padding: calc(var(--spacing) * 1);\n  }\n  .p-2 {\n    padding: calc(var(--spacing) * 2);\n  }\n  .p-3 {\n    padding: calc(var(--spacing) * 3);\n  }\n  .p-4 {\n    padding: calc(var(--spacing) * 4);\n  }\n  .p-6 {\n    padding: calc(var(--spacing) * 6);\n  }\n  .p-8 {\n    padding: calc(var(--spacing) * 8);\n  }\n  .p-\\[3px\\] {\n    padding: 3px;\n  }\n  .p-px {\n    padding: 1px;\n  }\n  .px-1 {\n    padding-inline: calc(var(--spacing) * 1);\n  }\n  .px-1\\.5 {\n    padding-inline: calc(var(--spacing) * 1.5);\n  }\n  .px-2 {\n    padding-inline: calc(var(--spacing) * 2);\n  }\n  .px-2\\.5 {\n    padding-inline: calc(var(--spacing) * 2.5);\n  }\n  .px-3 {\n    padding-inline: calc(var(--spacing) * 3);\n  }\n  .px-4 {\n    padding-inline: calc(var(--spacing) * 4);\n  }\n  .px-6 {\n    padding-inline: calc(var(--spacing) * 6);\n  }\n  .px-8 {\n    padding-inline: calc(var(--spacing) * 8);\n  }\n  .px-12 {\n    padding-inline: calc(var(--spacing) * 12);\n  }\n  .py-0\\.5 {\n    padding-block: calc(var(--spacing) * 0.5);\n  }\n  .py-1 {\n    padding-block: calc(var(--spacing) * 1);\n  }\n  .py-1\\.5 {\n    padding-block: calc(var(--spacing) * 1.5);\n  }\n  .py-2 {\n    padding-block: calc(var(--spacing) * 2);\n  }\n  .py-2\\.5 {\n    padding-block: calc(var(--spacing) * 2.5);\n  }\n  .py-3 {\n    padding-block: calc(var(--spacing) * 3);\n  }\n  .py-4 {\n    padding-block: calc(var(--spacing) * 4);\n  }\n  .py-6 {\n    padding-block: calc(var(--spacing) * 6);\n  }\n  .py-8 {\n    padding-block: calc(var(--spacing) * 8);\n  }\n  .py-12 {\n    padding-block: calc(var(--spacing) * 12);\n  }\n  .py-16 {\n    padding-block: calc(var(--spacing) * 16);\n  }\n  .py-20 {\n    padding-block: calc(var(--spacing) * 20);\n  }\n  .pt-0 {\n    padding-top: calc(var(--spacing) * 0);\n  }\n  .pt-1 {\n    padding-top: calc(var(--spacing) * 1);\n  }\n  .pt-3 {\n    padding-top: calc(var(--spacing) * 3);\n  }\n  .pt-4 {\n    padding-top: calc(var(--spacing) * 4);\n  }\n  .pt-6 {\n    padding-top: calc(var(--spacing) * 6);\n  }\n  .pt-8 {\n    padding-top: calc(var(--spacing) * 8);\n  }\n  .pt-12 {\n    padding-top: calc(var(--spacing) * 12);\n  }\n  .pt-16 {\n    padding-top: calc(var(--spacing) * 16);\n  }\n  .pt-20 {\n    padding-top: calc(var(--spacing) * 20);\n  }\n  .pt-32 {\n    padding-top: calc(var(--spacing) * 32);\n  }\n  .pt-40 {\n    padding-top: calc(var(--spacing) * 40);\n  }\n  .pr-2 {\n    padding-right: calc(var(--spacing) * 2);\n  }\n  .pr-2\\.5 {\n    padding-right: calc(var(--spacing) * 2.5);\n  }\n  .pr-8 {\n    padding-right: calc(var(--spacing) * 8);\n  }\n  .pr-16 {\n    padding-right: calc(var(--spacing) * 16);\n  }\n  .pb-3 {\n    padding-bottom: calc(var(--spacing) * 3);\n  }\n  .pb-4 {\n    padding-bottom: calc(var(--spacing) * 4);\n  }\n  .pb-8 {\n    padding-bottom: calc(var(--spacing) * 8);\n  }\n  .pb-16 {\n    padding-bottom: calc(var(--spacing) * 16);\n  }\n  .pb-20 {\n    padding-bottom: calc(var(--spacing) * 20);\n  }\n  .pl-2 {\n    padding-left: calc(var(--spacing) * 2);\n  }\n  .pl-4 {\n    padding-left: calc(var(--spacing) * 4);\n  }\n  .pl-8 {\n    padding-left: calc(var(--spacing) * 8);\n  }\n  .text-center {\n    text-align: center;\n  }\n  .text-left {\n    text-align: left;\n  }\n  .align-middle {\n    vertical-align: middle;\n  }\n  .font-mono {\n    font-family: var(--font-geist-mono);\n  }\n  .font-sans {\n    font-family: var(--font-geist-sans);\n  }\n  .text-2xl {\n    font-size: var(--text-2xl);\n    line-height: var(--tw-leading, var(--text-2xl--line-height));\n  }\n  .text-3xl {\n    font-size: var(--text-3xl);\n    line-height: var(--tw-leading, var(--text-3xl--line-height));\n  }\n  .text-4xl {\n    font-size: var(--text-4xl);\n    line-height: var(--tw-leading, var(--text-4xl--line-height));\n  }\n  .text-6xl {\n    font-size: var(--text-6xl);\n    line-height: var(--tw-leading, var(--text-6xl--line-height));\n  }\n  .text-base {\n    font-size: var(--text-base);\n    line-height: var(--tw-leading, var(--text-base--line-height));\n  }\n  .text-lg {\n    font-size: var(--text-lg);\n    line-height: var(--tw-leading, var(--text-lg--line-height));\n  }\n  .text-sm {\n    font-size: var(--text-sm);\n    line-height: var(--tw-leading, var(--text-sm--line-height));\n  }\n  .text-xl {\n    font-size: var(--text-xl);\n    line-height: var(--tw-leading, var(--text-xl--line-height));\n  }\n  .text-xs {\n    font-size: var(--text-xs);\n    line-height: var(--tw-leading, var(--text-xs--line-height));\n  }\n  .text-\\[0\\.8rem\\] {\n    font-size: 0.8rem;\n  }\n  .text-\\[16rem\\] {\n    font-size: 16rem;\n  }\n  .leading-7 {\n    --tw-leading: calc(var(--spacing) * 7);\n    line-height: calc(var(--spacing) * 7);\n  }\n  .leading-none {\n    --tw-leading: 1;\n    line-height: 1;\n  }\n  .leading-relaxed {\n    --tw-leading: var(--leading-relaxed);\n    line-height: var(--leading-relaxed);\n  }\n  .leading-tight {\n    --tw-leading: var(--leading-tight);\n    line-height: var(--leading-tight);\n  }\n  .font-bold {\n    --tw-font-weight: var(--font-weight-bold);\n    font-weight: var(--font-weight-bold);\n  }\n  .font-extrabold {\n    --tw-font-weight: var(--font-weight-extrabold);\n    font-weight: var(--font-weight-extrabold);\n  }\n  .font-medium {\n    --tw-font-weight: var(--font-weight-medium);\n    font-weight: var(--font-weight-medium);\n  }\n  .font-normal {\n    --tw-font-weight: var(--font-weight-normal);\n    font-weight: var(--font-weight-normal);\n  }\n  .font-semibold {\n    --tw-font-weight: var(--font-weight-semibold);\n    font-weight: var(--font-weight-semibold);\n  }\n  .tracking-normal {\n    --tw-tracking: var(--tracking-normal);\n    letter-spacing: var(--tracking-normal);\n  }\n  .tracking-tight {\n    --tw-tracking: var(--tracking-tight);\n    letter-spacing: var(--tracking-tight);\n  }\n  .tracking-tighter {\n    --tw-tracking: var(--tracking-tighter);\n    letter-spacing: var(--tracking-tighter);\n  }\n  .tracking-wider {\n    --tw-tracking: var(--tracking-wider);\n    letter-spacing: var(--tracking-wider);\n  }\n  .tracking-widest {\n    --tw-tracking: var(--tracking-widest);\n    letter-spacing: var(--tracking-widest);\n  }\n  .text-balance {\n    text-wrap: balance;\n  }\n  .break-words {\n    overflow-wrap: break-word;\n  }\n  .whitespace-nowrap {\n    white-space: nowrap;\n  }\n  .text-accent-foreground {\n    color: var(--accent-foreground);\n  }\n  .text-blue-400 {\n    color: var(--color-blue-400);\n  }\n  .text-blue-500 {\n    color: var(--color-blue-500);\n  }\n  .text-blue-600 {\n    color: var(--color-blue-600);\n  }\n  .text-blue-700 {\n    color: var(--color-blue-700);\n  }\n  .text-card-foreground {\n    color: var(--card-foreground);\n  }\n  .text-current {\n    color: currentcolor;\n  }\n  .text-cyan-400 {\n    color: var(--color-cyan-400);\n  }\n  .text-cyan-500 {\n    color: var(--color-cyan-500);\n  }\n  .text-destructive {\n    color: var(--destructive);\n  }\n  .text-foreground {\n    color: var(--foreground);\n  }\n  .text-gray-200 {\n    color: var(--color-gray-200);\n  }\n  .text-gray-300 {\n    color: var(--color-gray-300);\n  }\n  .text-gray-400 {\n    color: var(--color-gray-400);\n  }\n  .text-gray-500 {\n    color: var(--color-gray-500);\n  }\n  .text-green-400 {\n    color: var(--color-green-400);\n  }\n  .text-green-500 {\n    color: var(--color-green-500);\n  }\n  .text-muted-foreground {\n    color: var(--muted-foreground);\n  }\n  .text-neutral-300 {\n    color: var(--color-neutral-300);\n  }\n  .text-neutral-400 {\n    color: var(--color-neutral-400);\n  }\n  .text-orange-400 {\n    color: var(--color-orange-400);\n  }\n  .text-orange-500 {\n    color: var(--color-orange-500);\n  }\n  .text-orange-600 {\n    color: var(--color-orange-600);\n  }\n  .text-popover-foreground {\n    color: var(--popover-foreground);\n  }\n  .text-primary {\n    color: var(--primary);\n  }\n  .text-primary-foreground {\n    color: var(--primary-foreground);\n  }\n  .text-purple-500 {\n    color: var(--color-purple-500);\n  }\n  .text-purple-600 {\n    color: var(--color-purple-600);\n  }\n  .text-red-500 {\n    color: var(--color-red-500);\n  }\n  .text-red-600 {\n    color: var(--color-red-600);\n  }\n  .text-secondary-foreground {\n    color: var(--secondary-foreground);\n  }\n  .text-sidebar-foreground {\n    color: var(--sidebar-foreground);\n  }\n  .text-sidebar-foreground\\/70 {\n    color: var(--sidebar-foreground);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--sidebar-foreground) 70%, transparent);\n    }\n  }\n  .text-white {\n    color: var(--color-white);\n  }\n  .text-white\\/50 {\n    color: color-mix(in srgb, #fff 50%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--color-white) 50%, transparent);\n    }\n  }\n  .text-white\\/60 {\n    color: color-mix(in srgb, #fff 60%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--color-white) 60%, transparent);\n    }\n  }\n  .text-white\\/70 {\n    color: color-mix(in srgb, #fff 70%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      color: color-mix(in oklab, var(--color-white) 70%, transparent);\n    }\n  }\n  .text-yellow-400 {\n    color: var(--color-yellow-400);\n  }\n  .capitalize {\n    text-transform: capitalize;\n  }\n  .uppercase {\n    text-transform: uppercase;\n  }\n  .italic {\n    font-style: italic;\n  }\n  .tabular-nums {\n    --tw-numeric-spacing: tabular-nums;\n    font-variant-numeric: var(--tw-ordinal,) var(--tw-slashed-zero,) var(--tw-numeric-figure,) var(--tw-numeric-spacing,) var(--tw-numeric-fraction,);\n  }\n  .underline {\n    text-decoration-line: underline;\n  }\n  .underline-offset-4 {\n    text-underline-offset: 4px;\n  }\n  .antialiased {\n    -webkit-font-smoothing: antialiased;\n    -moz-osx-font-smoothing: grayscale;\n  }\n  .opacity-0 {\n    opacity: 0%;\n  }\n  .opacity-15 {\n    opacity: 15%;\n  }\n  .opacity-20 {\n    opacity: 20%;\n  }\n  .opacity-30 {\n    opacity: 30%;\n  }\n  .opacity-50 {\n    opacity: 50%;\n  }\n  .opacity-60 {\n    opacity: 60%;\n  }\n  .opacity-70 {\n    opacity: 70%;\n  }\n  .opacity-100 {\n    opacity: 100%;\n  }\n  .opacity-\\[0\\.02\\] {\n    opacity: 0.02;\n  }\n  .shadow {\n    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-2xl {\n    --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, rgb(0 0 0 / 0.25));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-\\[0_0_0_1px_hsl\\(var\\(--sidebar-border\\)\\)\\] {\n    --tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-border)));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-lg {\n    --tw-shadow: 0 10px 15px -3px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 4px 6px -4px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-md {\n    --tw-shadow: 0 4px 6px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 2px 4px -2px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-none {\n    --tw-shadow: 0 0 #0000;\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-sm {\n    --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-xl {\n    --tw-shadow: 0 20px 25px -5px var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 8px 10px -6px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-xs {\n    --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .ring-0 {\n    --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n    box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n  }\n  .shadow-black\\/20 {\n    --tw-shadow-color: color-mix(in srgb, #000 20%, transparent);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-black) 20%, transparent) var(--tw-shadow-alpha), transparent);\n    }\n  }\n  .ring-ring\\/50 {\n    --tw-ring-color: var(--ring);\n    @supports (color: color-mix(in lab, red, red)) {\n      --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);\n    }\n  }\n  .ring-sidebar-ring {\n    --tw-ring-color: var(--sidebar-ring);\n  }\n  .ring-offset-background {\n    --tw-ring-offset-color: var(--background);\n  }\n  .outline-hidden {\n    --tw-outline-style: none;\n    outline-style: none;\n    @media (forced-colors: active) {\n      outline: 2px solid transparent;\n      outline-offset: 2px;\n    }\n  }\n  .outline {\n    outline-style: var(--tw-outline-style);\n    outline-width: 1px;\n  }\n  .blur {\n    --tw-blur: blur(8px);\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .blur-2xl {\n    --tw-blur: blur(var(--blur-2xl));\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .blur-3xl {\n    --tw-blur: blur(var(--blur-3xl));\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .blur-xl {\n    --tw-blur: blur(var(--blur-xl));\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .drop-shadow-lg {\n    --tw-drop-shadow-size: drop-shadow(0 4px 4px var(--tw-drop-shadow-color, rgb(0 0 0 / 0.15)));\n    --tw-drop-shadow: drop-shadow(var(--drop-shadow-lg));\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .grayscale {\n    --tw-grayscale: grayscale(100%);\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .filter {\n    filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n  }\n  .backdrop-blur-md {\n    --tw-backdrop-blur: blur(var(--blur-md));\n    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n  }\n  .backdrop-blur-sm {\n    --tw-backdrop-blur: blur(var(--blur-sm));\n    -webkit-backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n    backdrop-filter: var(--tw-backdrop-blur,) var(--tw-backdrop-brightness,) var(--tw-backdrop-contrast,) var(--tw-backdrop-grayscale,) var(--tw-backdrop-hue-rotate,) var(--tw-backdrop-invert,) var(--tw-backdrop-opacity,) var(--tw-backdrop-saturate,) var(--tw-backdrop-sepia,);\n  }\n  .transition {\n    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to, opacity, box-shadow, transform, translate, scale, rotate, filter, -webkit-backdrop-filter, backdrop-filter, display, visibility, content-visibility, overlay, pointer-events;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-\\[color\\,box-shadow\\] {\n    transition-property: color,box-shadow;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-\\[left\\,right\\,width\\] {\n    transition-property: left,right,width;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-\\[margin\\,opacity\\] {\n    transition-property: margin,opacity;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-\\[width\\,height\\,padding\\] {\n    transition-property: width,height,padding;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-\\[width\\] {\n    transition-property: width;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-all {\n    transition-property: all;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-colors {\n    transition-property: color, background-color, border-color, outline-color, text-decoration-color, fill, stroke, --tw-gradient-from, --tw-gradient-via, --tw-gradient-to;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-opacity {\n    transition-property: opacity;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-shadow {\n    transition-property: box-shadow;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-transform {\n    transition-property: transform, translate, scale, rotate;\n    transition-timing-function: var(--tw-ease, var(--default-transition-timing-function));\n    transition-duration: var(--tw-duration, var(--default-transition-duration));\n  }\n  .transition-none {\n    transition-property: none;\n  }\n  .duration-200 {\n    --tw-duration: 200ms;\n    transition-duration: 200ms;\n  }\n  .duration-300 {\n    --tw-duration: 300ms;\n    transition-duration: 300ms;\n  }\n  .duration-500 {\n    --tw-duration: 500ms;\n    transition-duration: 500ms;\n  }\n  .duration-1000 {\n    --tw-duration: 1000ms;\n    transition-duration: 1000ms;\n  }\n  .ease-in-out {\n    --tw-ease: var(--ease-in-out);\n    transition-timing-function: var(--ease-in-out);\n  }\n  .ease-linear {\n    --tw-ease: linear;\n    transition-timing-function: linear;\n  }\n  .prose-neutral {\n    --tw-prose-body: oklch(37.1% 0 0);\n    --tw-prose-headings: oklch(20.5% 0 0);\n    --tw-prose-lead: oklch(43.9% 0 0);\n    --tw-prose-links: oklch(20.5% 0 0);\n    --tw-prose-bold: oklch(20.5% 0 0);\n    --tw-prose-counters: oklch(55.6% 0 0);\n    --tw-prose-bullets: oklch(87% 0 0);\n    --tw-prose-hr: oklch(92.2% 0 0);\n    --tw-prose-quotes: oklch(20.5% 0 0);\n    --tw-prose-quote-borders: oklch(92.2% 0 0);\n    --tw-prose-captions: oklch(55.6% 0 0);\n    --tw-prose-kbd: oklch(20.5% 0 0);\n    --tw-prose-kbd-shadows: NaN NaN NaN;\n    --tw-prose-code: oklch(20.5% 0 0);\n    --tw-prose-pre-code: oklch(92.2% 0 0);\n    --tw-prose-pre-bg: oklch(26.9% 0 0);\n    --tw-prose-th-borders: oklch(87% 0 0);\n    --tw-prose-td-borders: oklch(92.2% 0 0);\n    --tw-prose-invert-body: oklch(87% 0 0);\n    --tw-prose-invert-headings: #fff;\n    --tw-prose-invert-lead: oklch(70.8% 0 0);\n    --tw-prose-invert-links: #fff;\n    --tw-prose-invert-bold: #fff;\n    --tw-prose-invert-counters: oklch(70.8% 0 0);\n    --tw-prose-invert-bullets: oklch(43.9% 0 0);\n    --tw-prose-invert-hr: oklch(37.1% 0 0);\n    --tw-prose-invert-quotes: oklch(97% 0 0);\n    --tw-prose-invert-quote-borders: oklch(37.1% 0 0);\n    --tw-prose-invert-captions: oklch(70.8% 0 0);\n    --tw-prose-invert-kbd: #fff;\n    --tw-prose-invert-kbd-shadows: 255 255 255;\n    --tw-prose-invert-code: #fff;\n    --tw-prose-invert-pre-code: oklch(87% 0 0);\n    --tw-prose-invert-pre-bg: rgb(0 0 0 / 50%);\n    --tw-prose-invert-th-borders: oklch(43.9% 0 0);\n    --tw-prose-invert-td-borders: oklch(37.1% 0 0);\n  }\n  .prose {\n    --tw-prose-body: var(--color-foreground);\n    --tw-prose-headings: var(--color-foreground);\n    --tw-prose-lead: var(--color-muted-foreground);\n    --tw-prose-links: var(--color-primary);\n    --tw-prose-bold: var(--color-foreground);\n    --tw-prose-counters: var(--color-foreground);\n    --tw-prose-bullets: var(--color-muted-foreground);\n    --tw-prose-hr: var(--color-muted-foreground);\n    --tw-prose-quotes: var(--color-muted-foreground);\n    --tw-prose-quote-borders: var(--color-border);\n    --tw-prose-captions: var(--color-muted-foreground);\n    --tw-prose-code: var(--color-foreground);\n    --tw-prose-pre-code: var(--color-foreground);\n    --tw-prose-pre-bg: var(--color-background);\n    --tw-prose-th-borders: var(--color-border);\n    --tw-prose-td-borders: var(--color-border);\n    --tw-prose-invert-body: var(--color-foreground);\n    --tw-prose-invert-headings: var(--color-foreground);\n    --tw-prose-invert-lead: var(--color-muted-foreground);\n    --tw-prose-invert-links: var(--color-primary);\n    --tw-prose-invert-bold: var(--color-foreground);\n    --tw-prose-invert-counters: var(--color-foreground);\n    --tw-prose-invert-bullets: var(--color-foreground);\n    --tw-prose-invert-hr: var(--color-muted-foreground);\n    --tw-prose-invert-quotes: var(--color-muted-foreground);\n    --tw-prose-invert-quote-borders: var(--color-border);\n    --tw-prose-invert-captions: var(--color-muted-foreground);\n    --tw-prose-invert-code: var(--color-foreground);\n    --tw-prose-invert-pre-code: var(--color-foreground);\n    --tw-prose-invert-pre-bg: var(--color-background);\n    --tw-prose-invert-th-borders: var(--color-border);\n    --tw-prose-invert-td-borders: var(--color-border);\n  }\n  .fade-in-0 {\n    --tw-enter-opacity: calc(0/100);\n    --tw-enter-opacity: 0;\n  }\n  .outline-none {\n    --tw-outline-style: none;\n    outline-style: none;\n  }\n  .select-none {\n    -webkit-user-select: none;\n    user-select: none;\n  }\n  .zoom-in-95 {\n    --tw-enter-scale: calc(95*1%);\n    --tw-enter-scale: .95;\n  }\n  .running {\n    animation-play-state: running;\n  }\n  .group-focus-within\\/menu-item\\:opacity-100 {\n    &:is(:where(.group\\/menu-item):focus-within *) {\n      opacity: 100%;\n    }\n  }\n  .group-hover\\:bg-white\\/10 {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, #fff 10%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-white) 10%, transparent);\n        }\n      }\n    }\n  }\n  .group-hover\\:text-orange-500 {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        color: var(--color-orange-500);\n      }\n    }\n  }\n  .group-hover\\:text-white {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        color: var(--color-white);\n      }\n    }\n  }\n  .group-hover\\:opacity-80 {\n    &:is(:where(.group):hover *) {\n      @media (hover: hover) {\n        opacity: 80%;\n      }\n    }\n  }\n  .group-hover\\/menu-item\\:opacity-100 {\n    &:is(:where(.group\\/menu-item):hover *) {\n      @media (hover: hover) {\n        opacity: 100%;\n      }\n    }\n  }\n  .group-has-data-\\[sidebar\\=menu-action\\]\\/menu-item\\:pr-8 {\n    &:is(:where(.group\\/menu-item):has(*[data-sidebar=\"menu-action\"]) *) {\n      padding-right: calc(var(--spacing) * 8);\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:-mt-8 {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      margin-top: calc(var(--spacing) * -8);\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:hidden {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      display: none;\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:size-8\\! {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      width: calc(var(--spacing) * 8) !important;\n      height: calc(var(--spacing) * 8) !important;\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:w-\\(--sidebar-width-icon\\) {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      width: var(--sidebar-width-icon);\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:w-\\[calc\\(var\\(--sidebar-width-icon\\)\\+\\(--spacing\\(4\\)\\)\\)\\] {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      width: calc(var(--sidebar-width-icon) + (calc(var(--spacing) * 4)));\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:w-\\[calc\\(var\\(--sidebar-width-icon\\)\\+\\(--spacing\\(4\\)\\)\\+2px\\)\\] {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      width: calc(var(--sidebar-width-icon) + (calc(var(--spacing) * 4)) + 2px);\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:overflow-hidden {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      overflow: hidden;\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:p-0\\! {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      padding: calc(var(--spacing) * 0) !important;\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:p-2\\! {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      padding: calc(var(--spacing) * 2) !important;\n    }\n  }\n  .group-data-\\[collapsible\\=icon\\]\\:opacity-0 {\n    &:is(:where(.group)[data-collapsible=\"icon\"] *) {\n      opacity: 0%;\n    }\n  }\n  .group-data-\\[collapsible\\=offcanvas\\]\\:right-\\[calc\\(var\\(--sidebar-width\\)\\*-1\\)\\] {\n    &:is(:where(.group)[data-collapsible=\"offcanvas\"] *) {\n      right: calc(var(--sidebar-width) * -1);\n    }\n  }\n  .group-data-\\[collapsible\\=offcanvas\\]\\:left-\\[calc\\(var\\(--sidebar-width\\)\\*-1\\)\\] {\n    &:is(:where(.group)[data-collapsible=\"offcanvas\"] *) {\n      left: calc(var(--sidebar-width) * -1);\n    }\n  }\n  .group-data-\\[collapsible\\=offcanvas\\]\\:w-0 {\n    &:is(:where(.group)[data-collapsible=\"offcanvas\"] *) {\n      width: calc(var(--spacing) * 0);\n    }\n  }\n  .group-data-\\[collapsible\\=offcanvas\\]\\:translate-x-0 {\n    &:is(:where(.group)[data-collapsible=\"offcanvas\"] *) {\n      --tw-translate-x: calc(var(--spacing) * 0);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .group-data-\\[disabled\\=true\\]\\:pointer-events-none {\n    &:is(:where(.group)[data-disabled=\"true\"] *) {\n      pointer-events: none;\n    }\n  }\n  .group-data-\\[disabled\\=true\\]\\:opacity-50 {\n    &:is(:where(.group)[data-disabled=\"true\"] *) {\n      opacity: 50%;\n    }\n  }\n  .group-data-\\[side\\=left\\]\\:-right-4 {\n    &:is(:where(.group)[data-side=\"left\"] *) {\n      right: calc(var(--spacing) * -4);\n    }\n  }\n  .group-data-\\[side\\=left\\]\\:border-r {\n    &:is(:where(.group)[data-side=\"left\"] *) {\n      border-right-style: var(--tw-border-style);\n      border-right-width: 1px;\n    }\n  }\n  .group-data-\\[side\\=right\\]\\:left-0 {\n    &:is(:where(.group)[data-side=\"right\"] *) {\n      left: calc(var(--spacing) * 0);\n    }\n  }\n  .group-data-\\[side\\=right\\]\\:rotate-180 {\n    &:is(:where(.group)[data-side=\"right\"] *) {\n      rotate: 180deg;\n    }\n  }\n  .group-data-\\[side\\=right\\]\\:border-l {\n    &:is(:where(.group)[data-side=\"right\"] *) {\n      border-left-style: var(--tw-border-style);\n      border-left-width: 1px;\n    }\n  }\n  .group-data-\\[state\\=open\\]\\:rotate-180 {\n    &:is(:where(.group)[data-state=\"open\"] *) {\n      rotate: 180deg;\n    }\n  }\n  .group-data-\\[variant\\=floating\\]\\:rounded-lg {\n    &:is(:where(.group)[data-variant=\"floating\"] *) {\n      border-radius: var(--radius);\n    }\n  }\n  .group-data-\\[variant\\=floating\\]\\:border {\n    &:is(:where(.group)[data-variant=\"floating\"] *) {\n      border-style: var(--tw-border-style);\n      border-width: 1px;\n    }\n  }\n  .group-data-\\[variant\\=floating\\]\\:border-sidebar-border {\n    &:is(:where(.group)[data-variant=\"floating\"] *) {\n      border-color: var(--sidebar-border);\n    }\n  }\n  .group-data-\\[variant\\=floating\\]\\:shadow-sm {\n    &:is(:where(.group)[data-variant=\"floating\"] *) {\n      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .group-data-\\[vaul-drawer-direction\\=bottom\\]\\/drawer-content\\:block {\n    &:is(:where(.group\\/drawer-content)[data-vaul-drawer-direction=\"bottom\"] *) {\n      display: block;\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:top-full {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      top: 100%;\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:mt-1\\.5 {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      margin-top: calc(var(--spacing) * 1.5);\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:overflow-hidden {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      overflow: hidden;\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:rounded-md {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      border-radius: calc(var(--radius) - 2px);\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:border {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      border-style: var(--tw-border-style);\n      border-width: 1px;\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:bg-popover {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      background-color: var(--popover);\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:text-popover-foreground {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      color: var(--popover-foreground);\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:shadow {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:duration-200 {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      --tw-duration: 200ms;\n      transition-duration: 200ms;\n    }\n  }\n  .peer-hover\\/menu-button\\:text-sidebar-accent-foreground {\n    &:is(:where(.peer\\/menu-button):hover ~ *) {\n      @media (hover: hover) {\n        color: var(--sidebar-accent-foreground);\n      }\n    }\n  }\n  .peer-disabled\\:cursor-not-allowed {\n    &:is(:where(.peer):disabled ~ *) {\n      cursor: not-allowed;\n    }\n  }\n  .peer-disabled\\:opacity-50 {\n    &:is(:where(.peer):disabled ~ *) {\n      opacity: 50%;\n    }\n  }\n  .peer-data-\\[active\\=true\\]\\/menu-button\\:text-sidebar-accent-foreground {\n    &:is(:where(.peer\\/menu-button)[data-active=\"true\"] ~ *) {\n      color: var(--sidebar-accent-foreground);\n    }\n  }\n  .peer-data-\\[size\\=default\\]\\/menu-button\\:top-1\\.5 {\n    &:is(:where(.peer\\/menu-button)[data-size=\"default\"] ~ *) {\n      top: calc(var(--spacing) * 1.5);\n    }\n  }\n  .peer-data-\\[size\\=lg\\]\\/menu-button\\:top-2\\.5 {\n    &:is(:where(.peer\\/menu-button)[data-size=\"lg\"] ~ *) {\n      top: calc(var(--spacing) * 2.5);\n    }\n  }\n  .peer-data-\\[size\\=sm\\]\\/menu-button\\:top-1 {\n    &:is(:where(.peer\\/menu-button)[data-size=\"sm\"] ~ *) {\n      top: calc(var(--spacing) * 1);\n    }\n  }\n  .selection\\:bg-primary {\n    & *::selection {\n      background-color: var(--primary);\n    }\n    &::selection {\n      background-color: var(--primary);\n    }\n  }\n  .selection\\:text-primary-foreground {\n    & *::selection {\n      color: var(--primary-foreground);\n    }\n    &::selection {\n      color: var(--primary-foreground);\n    }\n  }\n  .file\\:inline-flex {\n    &::file-selector-button {\n      display: inline-flex;\n    }\n  }\n  .file\\:h-7 {\n    &::file-selector-button {\n      height: calc(var(--spacing) * 7);\n    }\n  }\n  .file\\:border-0 {\n    &::file-selector-button {\n      border-style: var(--tw-border-style);\n      border-width: 0px;\n    }\n  }\n  .file\\:bg-transparent {\n    &::file-selector-button {\n      background-color: transparent;\n    }\n  }\n  .file\\:text-sm {\n    &::file-selector-button {\n      font-size: var(--text-sm);\n      line-height: var(--tw-leading, var(--text-sm--line-height));\n    }\n  }\n  .file\\:font-medium {\n    &::file-selector-button {\n      --tw-font-weight: var(--font-weight-medium);\n      font-weight: var(--font-weight-medium);\n    }\n  }\n  .file\\:text-foreground {\n    &::file-selector-button {\n      color: var(--foreground);\n    }\n  }\n  .placeholder\\:text-muted-foreground {\n    &::placeholder {\n      color: var(--muted-foreground);\n    }\n  }\n  .after\\:absolute {\n    &::after {\n      content: var(--tw-content);\n      position: absolute;\n    }\n  }\n  .after\\:-inset-2 {\n    &::after {\n      content: var(--tw-content);\n      inset: calc(var(--spacing) * -2);\n    }\n  }\n  .after\\:inset-y-0 {\n    &::after {\n      content: var(--tw-content);\n      inset-block: calc(var(--spacing) * 0);\n    }\n  }\n  .after\\:left-1\\/2 {\n    &::after {\n      content: var(--tw-content);\n      left: calc(1/2 * 100%);\n    }\n  }\n  .after\\:w-1 {\n    &::after {\n      content: var(--tw-content);\n      width: calc(var(--spacing) * 1);\n    }\n  }\n  .after\\:w-\\[2px\\] {\n    &::after {\n      content: var(--tw-content);\n      width: 2px;\n    }\n  }\n  .after\\:-translate-x-1\\/2 {\n    &::after {\n      content: var(--tw-content);\n      --tw-translate-x: calc(calc(1/2 * 100%) * -1);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .group-data-\\[collapsible\\=offcanvas\\]\\:after\\:left-full {\n    &:is(:where(.group)[data-collapsible=\"offcanvas\"] *) {\n      &::after {\n        content: var(--tw-content);\n        left: 100%;\n      }\n    }\n  }\n  .first\\:rounded-l-md {\n    &:first-child {\n      border-top-left-radius: calc(var(--radius) - 2px);\n      border-bottom-left-radius: calc(var(--radius) - 2px);\n    }\n  }\n  .first\\:border-l {\n    &:first-child {\n      border-left-style: var(--tw-border-style);\n      border-left-width: 1px;\n    }\n  }\n  .last\\:rounded-r-md {\n    &:last-child {\n      border-top-right-radius: calc(var(--radius) - 2px);\n      border-bottom-right-radius: calc(var(--radius) - 2px);\n    }\n  }\n  .last\\:border-b-0 {\n    &:last-child {\n      border-bottom-style: var(--tw-border-style);\n      border-bottom-width: 0px;\n    }\n  }\n  .focus-within\\:relative {\n    &:focus-within {\n      position: relative;\n    }\n  }\n  .focus-within\\:z-20 {\n    &:focus-within {\n      z-index: 20;\n    }\n  }\n  .hover\\:-translate-y-2 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-translate-y: calc(var(--spacing) * -2);\n        translate: var(--tw-translate-x) var(--tw-translate-y);\n      }\n    }\n  }\n  .hover\\:scale-105 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-scale-x: 105%;\n        --tw-scale-y: 105%;\n        --tw-scale-z: 105%;\n        scale: var(--tw-scale-x) var(--tw-scale-y);\n      }\n    }\n  }\n  .hover\\:border-orange-500\\/30 {\n    &:hover {\n      @media (hover: hover) {\n        border-color: color-mix(in srgb, oklch(70.5% 0.213 47.604) 30%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          border-color: color-mix(in oklab, var(--color-orange-500) 30%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:border-orange-600 {\n    &:hover {\n      @media (hover: hover) {\n        border-color: var(--color-orange-600);\n      }\n    }\n  }\n  .hover\\:bg-\\[\\#222222\\] {\n    &:hover {\n      @media (hover: hover) {\n        background-color: #222222;\n      }\n    }\n  }\n  .hover\\:bg-accent {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--accent);\n      }\n    }\n  }\n  .hover\\:bg-black\\/40 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, #000 40%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-black) 40%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-destructive\\/90 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--destructive);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--destructive) 90%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-gray-700 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-gray-700);\n      }\n    }\n  }\n  .hover\\:bg-muted {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--muted);\n      }\n    }\n  }\n  .hover\\:bg-muted\\/50 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--muted);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--muted) 50%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-neutral-600\\/60 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, oklch(43.9% 0 0) 60%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-neutral-600) 60%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-neutral-700\\/50 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, oklch(37.1% 0 0) 50%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-neutral-700) 50%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-neutral-700\\/70 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, oklch(37.1% 0 0) 70%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-neutral-700) 70%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-neutral-800\\/50 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, oklch(26.9% 0 0) 50%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-neutral-800) 50%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-orange-500\\/10 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, oklch(70.5% 0.213 47.604) 10%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-orange-500) 10%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-orange-600 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--color-orange-600);\n      }\n    }\n  }\n  .hover\\:bg-primary {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--primary);\n      }\n    }\n  }\n  .hover\\:bg-primary\\/90 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--primary);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--primary) 90%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-secondary\\/80 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--secondary);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--secondary) 80%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:bg-sidebar-accent {\n    &:hover {\n      @media (hover: hover) {\n        background-color: var(--sidebar-accent);\n      }\n    }\n  }\n  .hover\\:bg-transparent {\n    &:hover {\n      @media (hover: hover) {\n        background-color: transparent;\n      }\n    }\n  }\n  .hover\\:bg-white\\/10 {\n    &:hover {\n      @media (hover: hover) {\n        background-color: color-mix(in srgb, #fff 10%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--color-white) 10%, transparent);\n        }\n      }\n    }\n  }\n  .hover\\:from-orange-500\\/20 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-gradient-from: color-mix(in srgb, oklch(70.5% 0.213 47.604) 20%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-gradient-from: color-mix(in oklab, var(--color-orange-500) 20%, transparent);\n        }\n        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n      }\n    }\n  }\n  .hover\\:from-orange-700 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-gradient-from: var(--color-orange-700);\n        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n      }\n    }\n  }\n  .hover\\:to-orange-600\\/20 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-gradient-to: color-mix(in srgb, oklch(64.6% 0.222 41.116) 20%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-gradient-to: color-mix(in oklab, var(--color-orange-600) 20%, transparent);\n        }\n        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n      }\n    }\n  }\n  .hover\\:to-orange-800 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-gradient-to: var(--color-orange-800);\n        --tw-gradient-stops: var(--tw-gradient-via-stops, var(--tw-gradient-position), var(--tw-gradient-from) var(--tw-gradient-from-position), var(--tw-gradient-to) var(--tw-gradient-to-position));\n      }\n    }\n  }\n  .hover\\:text-accent-foreground {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--accent-foreground);\n      }\n    }\n  }\n  .hover\\:text-foreground {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--foreground);\n      }\n    }\n  }\n  .hover\\:text-muted-foreground {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--muted-foreground);\n      }\n    }\n  }\n  .hover\\:text-orange-300 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-orange-300);\n      }\n    }\n  }\n  .hover\\:text-orange-400 {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-orange-400);\n      }\n    }\n  }\n  .hover\\:text-primary-foreground {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--primary-foreground);\n      }\n    }\n  }\n  .hover\\:text-sidebar-accent-foreground {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--sidebar-accent-foreground);\n      }\n    }\n  }\n  .hover\\:text-white {\n    &:hover {\n      @media (hover: hover) {\n        color: var(--color-white);\n      }\n    }\n  }\n  .hover\\:underline {\n    &:hover {\n      @media (hover: hover) {\n        text-decoration-line: underline;\n      }\n    }\n  }\n  .hover\\:opacity-75 {\n    &:hover {\n      @media (hover: hover) {\n        opacity: 75%;\n      }\n    }\n  }\n  .hover\\:opacity-80 {\n    &:hover {\n      @media (hover: hover) {\n        opacity: 80%;\n      }\n    }\n  }\n  .hover\\:opacity-100 {\n    &:hover {\n      @media (hover: hover) {\n        opacity: 100%;\n      }\n    }\n  }\n  .hover\\:shadow-2xl {\n    &:hover {\n      @media (hover: hover) {\n        --tw-shadow: 0 25px 50px -12px var(--tw-shadow-color, rgb(0 0 0 / 0.25));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .hover\\:shadow-\\[0_0_0_1px_hsl\\(var\\(--sidebar-accent\\)\\)\\] {\n    &:hover {\n      @media (hover: hover) {\n        --tw-shadow: 0 0 0 1px var(--tw-shadow-color, hsl(var(--sidebar-accent)));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .hover\\:shadow-\\[0_0_10px_rgba\\(234\\,88\\,12\\,0\\.6\\)\\] {\n    &:hover {\n      @media (hover: hover) {\n        --tw-shadow: 0 0 10px var(--tw-shadow-color, rgba(234,88,12,0.6));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .hover\\:ring-4 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .hover\\:shadow-black\\/50 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-shadow-color: color-mix(in srgb, #000 50%, transparent);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-shadow-color: color-mix(in oklab, color-mix(in oklab, var(--color-black) 50%, transparent) var(--tw-shadow-alpha), transparent);\n        }\n      }\n    }\n  }\n  .hover\\:grayscale-0 {\n    &:hover {\n      @media (hover: hover) {\n        --tw-grayscale: grayscale(0%);\n        filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n      }\n    }\n  }\n  .hover\\:group-data-\\[collapsible\\=offcanvas\\]\\:bg-sidebar {\n    &:hover {\n      @media (hover: hover) {\n        &:is(:where(.group)[data-collapsible=\"offcanvas\"] *) {\n          background-color: var(--sidebar);\n        }\n      }\n    }\n  }\n  .hover\\:after\\:bg-sidebar-border {\n    &:hover {\n      @media (hover: hover) {\n        &::after {\n          content: var(--tw-content);\n          background-color: var(--sidebar-border);\n        }\n      }\n    }\n  }\n  .focus\\:z-10 {\n    &:focus {\n      z-index: 10;\n    }\n  }\n  .focus\\:bg-accent {\n    &:focus {\n      background-color: var(--accent);\n    }\n  }\n  .focus\\:bg-primary {\n    &:focus {\n      background-color: var(--primary);\n    }\n  }\n  .focus\\:text-accent-foreground {\n    &:focus {\n      color: var(--accent-foreground);\n    }\n  }\n  .focus\\:text-primary-foreground {\n    &:focus {\n      color: var(--primary-foreground);\n    }\n  }\n  .focus\\:underline {\n    &:focus {\n      text-decoration-line: underline;\n    }\n  }\n  .focus\\:ring-2 {\n    &:focus {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus\\:ring-ring {\n    &:focus {\n      --tw-ring-color: var(--ring);\n    }\n  }\n  .focus\\:ring-offset-2 {\n    &:focus {\n      --tw-ring-offset-width: 2px;\n      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n    }\n  }\n  .focus\\:outline-hidden {\n    &:focus {\n      --tw-outline-style: none;\n      outline-style: none;\n      @media (forced-colors: active) {\n        outline: 2px solid transparent;\n        outline-offset: 2px;\n      }\n    }\n  }\n  .focus\\:outline-none {\n    &:focus {\n      --tw-outline-style: none;\n      outline-style: none;\n    }\n  }\n  .focus-visible\\:z-10 {\n    &:focus-visible {\n      z-index: 10;\n    }\n  }\n  .focus-visible\\:border-ring {\n    &:focus-visible {\n      border-color: var(--ring);\n    }\n  }\n  .focus-visible\\:ring-1 {\n    &:focus-visible {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(1px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus-visible\\:ring-2 {\n    &:focus-visible {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(2px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus-visible\\:ring-4 {\n    &:focus-visible {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(4px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus-visible\\:ring-\\[3px\\] {\n    &:focus-visible {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .focus-visible\\:ring-destructive\\/20 {\n    &:focus-visible {\n      --tw-ring-color: var(--destructive);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);\n      }\n    }\n  }\n  .focus-visible\\:ring-ring {\n    &:focus-visible {\n      --tw-ring-color: var(--ring);\n    }\n  }\n  .focus-visible\\:ring-ring\\/50 {\n    &:focus-visible {\n      --tw-ring-color: var(--ring);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);\n      }\n    }\n  }\n  .focus-visible\\:ring-offset-1 {\n    &:focus-visible {\n      --tw-ring-offset-width: 1px;\n      --tw-ring-offset-shadow: var(--tw-ring-inset,) 0 0 0 var(--tw-ring-offset-width) var(--tw-ring-offset-color);\n    }\n  }\n  .focus-visible\\:outline-hidden {\n    &:focus-visible {\n      --tw-outline-style: none;\n      outline-style: none;\n      @media (forced-colors: active) {\n        outline: 2px solid transparent;\n        outline-offset: 2px;\n      }\n    }\n  }\n  .focus-visible\\:outline-1 {\n    &:focus-visible {\n      outline-style: var(--tw-outline-style);\n      outline-width: 1px;\n    }\n  }\n  .focus-visible\\:outline-ring {\n    &:focus-visible {\n      outline-color: var(--ring);\n    }\n  }\n  .active\\:bg-sidebar-accent {\n    &:active {\n      background-color: var(--sidebar-accent);\n    }\n  }\n  .active\\:text-sidebar-accent-foreground {\n    &:active {\n      color: var(--sidebar-accent-foreground);\n    }\n  }\n  .disabled\\:pointer-events-none {\n    &:disabled {\n      pointer-events: none;\n    }\n  }\n  .disabled\\:cursor-not-allowed {\n    &:disabled {\n      cursor: not-allowed;\n    }\n  }\n  .disabled\\:opacity-50 {\n    &:disabled {\n      opacity: 50%;\n    }\n  }\n  .in-data-\\[side\\=left\\]\\:cursor-w-resize {\n    :where(*[data-side=\"left\"]) & {\n      cursor: w-resize;\n    }\n  }\n  .in-data-\\[side\\=right\\]\\:cursor-e-resize {\n    :where(*[data-side=\"right\"]) & {\n      cursor: e-resize;\n    }\n  }\n  .has-disabled\\:opacity-50 {\n    &:has(*:disabled) {\n      opacity: 50%;\n    }\n  }\n  .has-data-\\[slot\\=card-action\\]\\:grid-cols-\\[1fr_auto\\] {\n    &:has(*[data-slot=\"card-action\"]) {\n      grid-template-columns: 1fr auto;\n    }\n  }\n  .has-data-\\[variant\\=inset\\]\\:bg-sidebar {\n    &:has(*[data-variant=\"inset\"]) {\n      background-color: var(--sidebar);\n    }\n  }\n  .has-\\[\\>svg\\]\\:grid-cols-\\[calc\\(var\\(--spacing\\)\\*4\\)_1fr\\] {\n    &:has(>svg) {\n      grid-template-columns: calc(var(--spacing) * 4) 1fr;\n    }\n  }\n  .has-\\[\\>svg\\]\\:gap-x-3 {\n    &:has(>svg) {\n      column-gap: calc(var(--spacing) * 3);\n    }\n  }\n  .has-\\[\\>svg\\]\\:px-2\\.5 {\n    &:has(>svg) {\n      padding-inline: calc(var(--spacing) * 2.5);\n    }\n  }\n  .has-\\[\\>svg\\]\\:px-3 {\n    &:has(>svg) {\n      padding-inline: calc(var(--spacing) * 3);\n    }\n  }\n  .has-\\[\\>svg\\]\\:px-4 {\n    &:has(>svg) {\n      padding-inline: calc(var(--spacing) * 4);\n    }\n  }\n  .aria-disabled\\:pointer-events-none {\n    &[aria-disabled=\"true\"] {\n      pointer-events: none;\n    }\n  }\n  .aria-disabled\\:opacity-50 {\n    &[aria-disabled=\"true\"] {\n      opacity: 50%;\n    }\n  }\n  .aria-invalid\\:border-destructive {\n    &[aria-invalid=\"true\"] {\n      border-color: var(--destructive);\n    }\n  }\n  .aria-invalid\\:ring-destructive\\/20 {\n    &[aria-invalid=\"true\"] {\n      --tw-ring-color: var(--destructive);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);\n      }\n    }\n  }\n  .aria-selected\\:bg-accent {\n    &[aria-selected=\"true\"] {\n      background-color: var(--accent);\n    }\n  }\n  .aria-selected\\:bg-primary {\n    &[aria-selected=\"true\"] {\n      background-color: var(--primary);\n    }\n  }\n  .aria-selected\\:text-accent-foreground {\n    &[aria-selected=\"true\"] {\n      color: var(--accent-foreground);\n    }\n  }\n  .aria-selected\\:text-muted-foreground {\n    &[aria-selected=\"true\"] {\n      color: var(--muted-foreground);\n    }\n  }\n  .aria-selected\\:text-primary-foreground {\n    &[aria-selected=\"true\"] {\n      color: var(--primary-foreground);\n    }\n  }\n  .aria-selected\\:opacity-100 {\n    &[aria-selected=\"true\"] {\n      opacity: 100%;\n    }\n  }\n  .data-\\[active\\=true\\]\\:z-10 {\n    &[data-active=\"true\"] {\n      z-index: 10;\n    }\n  }\n  .data-\\[active\\=true\\]\\:border-ring {\n    &[data-active=\"true\"] {\n      border-color: var(--ring);\n    }\n  }\n  .data-\\[active\\=true\\]\\:bg-accent\\/50 {\n    &[data-active=\"true\"] {\n      background-color: var(--accent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--accent) 50%, transparent);\n      }\n    }\n  }\n  .data-\\[active\\=true\\]\\:bg-sidebar-accent {\n    &[data-active=\"true\"] {\n      background-color: var(--sidebar-accent);\n    }\n  }\n  .data-\\[active\\=true\\]\\:font-medium {\n    &[data-active=\"true\"] {\n      --tw-font-weight: var(--font-weight-medium);\n      font-weight: var(--font-weight-medium);\n    }\n  }\n  .data-\\[active\\=true\\]\\:text-accent-foreground {\n    &[data-active=\"true\"] {\n      color: var(--accent-foreground);\n    }\n  }\n  .data-\\[active\\=true\\]\\:text-sidebar-accent-foreground {\n    &[data-active=\"true\"] {\n      color: var(--sidebar-accent-foreground);\n    }\n  }\n  .data-\\[active\\=true\\]\\:ring-\\[3px\\] {\n    &[data-active=\"true\"] {\n      --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(3px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .data-\\[active\\=true\\]\\:ring-ring\\/50 {\n    &[data-active=\"true\"] {\n      --tw-ring-color: var(--ring);\n      @supports (color: color-mix(in lab, red, red)) {\n        --tw-ring-color: color-mix(in oklab, var(--ring) 50%, transparent);\n      }\n    }\n  }\n  .data-\\[active\\=true\\]\\:hover\\:bg-accent {\n    &[data-active=\"true\"] {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--accent);\n        }\n      }\n    }\n  }\n  .data-\\[active\\=true\\]\\:focus\\:bg-accent {\n    &[data-active=\"true\"] {\n      &:focus {\n        background-color: var(--accent);\n      }\n    }\n  }\n  .data-\\[active\\=true\\]\\:aria-invalid\\:border-destructive {\n    &[data-active=\"true\"] {\n      &[aria-invalid=\"true\"] {\n        border-color: var(--destructive);\n      }\n    }\n  }\n  .data-\\[active\\=true\\]\\:aria-invalid\\:ring-destructive\\/20 {\n    &[data-active=\"true\"] {\n      &[aria-invalid=\"true\"] {\n        --tw-ring-color: var(--destructive);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-ring-color: color-mix(in oklab, var(--destructive) 20%, transparent);\n        }\n      }\n    }\n  }\n  .data-\\[disabled\\]\\:pointer-events-none {\n    &[data-disabled] {\n      pointer-events: none;\n    }\n  }\n  .data-\\[disabled\\]\\:opacity-50 {\n    &[data-disabled] {\n      opacity: 50%;\n    }\n  }\n  .data-\\[disabled\\=true\\]\\:pointer-events-none {\n    &[data-disabled=\"true\"] {\n      pointer-events: none;\n    }\n  }\n  .data-\\[disabled\\=true\\]\\:opacity-50 {\n    &[data-disabled=\"true\"] {\n      opacity: 50%;\n    }\n  }\n  .data-\\[error\\=true\\]\\:text-destructive {\n    &[data-error=\"true\"] {\n      color: var(--destructive);\n    }\n  }\n  .data-\\[inset\\]\\:pl-8 {\n    &[data-inset] {\n      padding-left: calc(var(--spacing) * 8);\n    }\n  }\n  .data-\\[motion\\=from-end\\]\\:slide-in-from-right-52 {\n    &[data-motion=\"from-end\"] {\n      --tw-enter-translate-x: calc(52*var(--spacing));\n    }\n  }\n  .data-\\[motion\\=from-start\\]\\:slide-in-from-left-52 {\n    &[data-motion=\"from-start\"] {\n      --tw-enter-translate-x: calc(52*var(--spacing)*-1);\n    }\n  }\n  .data-\\[motion\\=to-end\\]\\:slide-out-to-right-52 {\n    &[data-motion=\"to-end\"] {\n      --tw-exit-translate-x: calc(52*var(--spacing));\n    }\n  }\n  .data-\\[motion\\=to-start\\]\\:slide-out-to-left-52 {\n    &[data-motion=\"to-start\"] {\n      --tw-exit-translate-x: calc(52*var(--spacing)*-1);\n    }\n  }\n  .data-\\[motion\\^\\=from-\\]\\:animate-in {\n    &[data-motion^=\"from-\"] {\n      animation: enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease);\n    }\n  }\n  .data-\\[motion\\^\\=from-\\]\\:fade-in {\n    &[data-motion^=\"from-\"] {\n      --tw-enter-opacity: 0;\n    }\n  }\n  .data-\\[motion\\^\\=to-\\]\\:animate-out {\n    &[data-motion^=\"to-\"] {\n      animation: exit var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease);\n    }\n  }\n  .data-\\[motion\\^\\=to-\\]\\:fade-out {\n    &[data-motion^=\"to-\"] {\n      --tw-exit-opacity: 0;\n    }\n  }\n  .data-\\[orientation\\=horizontal\\]\\:h-1\\.5 {\n    &[data-orientation=\"horizontal\"] {\n      height: calc(var(--spacing) * 1.5);\n    }\n  }\n  .data-\\[orientation\\=horizontal\\]\\:h-full {\n    &[data-orientation=\"horizontal\"] {\n      height: 100%;\n    }\n  }\n  .data-\\[orientation\\=horizontal\\]\\:h-px {\n    &[data-orientation=\"horizontal\"] {\n      height: 1px;\n    }\n  }\n  .data-\\[orientation\\=horizontal\\]\\:w-full {\n    &[data-orientation=\"horizontal\"] {\n      width: 100%;\n    }\n  }\n  .data-\\[orientation\\=vertical\\]\\:h-full {\n    &[data-orientation=\"vertical\"] {\n      height: 100%;\n    }\n  }\n  .data-\\[orientation\\=vertical\\]\\:min-h-44 {\n    &[data-orientation=\"vertical\"] {\n      min-height: calc(var(--spacing) * 44);\n    }\n  }\n  .data-\\[orientation\\=vertical\\]\\:w-1\\.5 {\n    &[data-orientation=\"vertical\"] {\n      width: calc(var(--spacing) * 1.5);\n    }\n  }\n  .data-\\[orientation\\=vertical\\]\\:w-auto {\n    &[data-orientation=\"vertical\"] {\n      width: auto;\n    }\n  }\n  .data-\\[orientation\\=vertical\\]\\:w-full {\n    &[data-orientation=\"vertical\"] {\n      width: 100%;\n    }\n  }\n  .data-\\[orientation\\=vertical\\]\\:w-px {\n    &[data-orientation=\"vertical\"] {\n      width: 1px;\n    }\n  }\n  .data-\\[orientation\\=vertical\\]\\:flex-col {\n    &[data-orientation=\"vertical\"] {\n      flex-direction: column;\n    }\n  }\n  .data-\\[panel-group-direction\\=vertical\\]\\:h-px {\n    &[data-panel-group-direction=\"vertical\"] {\n      height: 1px;\n    }\n  }\n  .data-\\[panel-group-direction\\=vertical\\]\\:w-full {\n    &[data-panel-group-direction=\"vertical\"] {\n      width: 100%;\n    }\n  }\n  .data-\\[panel-group-direction\\=vertical\\]\\:flex-col {\n    &[data-panel-group-direction=\"vertical\"] {\n      flex-direction: column;\n    }\n  }\n  .data-\\[panel-group-direction\\=vertical\\]\\:after\\:left-0 {\n    &[data-panel-group-direction=\"vertical\"] {\n      &::after {\n        content: var(--tw-content);\n        left: calc(var(--spacing) * 0);\n      }\n    }\n  }\n  .data-\\[panel-group-direction\\=vertical\\]\\:after\\:h-1 {\n    &[data-panel-group-direction=\"vertical\"] {\n      &::after {\n        content: var(--tw-content);\n        height: calc(var(--spacing) * 1);\n      }\n    }\n  }\n  .data-\\[panel-group-direction\\=vertical\\]\\:after\\:w-full {\n    &[data-panel-group-direction=\"vertical\"] {\n      &::after {\n        content: var(--tw-content);\n        width: 100%;\n      }\n    }\n  }\n  .data-\\[panel-group-direction\\=vertical\\]\\:after\\:translate-x-0 {\n    &[data-panel-group-direction=\"vertical\"] {\n      &::after {\n        content: var(--tw-content);\n        --tw-translate-x: calc(var(--spacing) * 0);\n        translate: var(--tw-translate-x) var(--tw-translate-y);\n      }\n    }\n  }\n  .data-\\[panel-group-direction\\=vertical\\]\\:after\\:-translate-y-1\\/2 {\n    &[data-panel-group-direction=\"vertical\"] {\n      &::after {\n        content: var(--tw-content);\n        --tw-translate-y: calc(calc(1/2 * 100%) * -1);\n        translate: var(--tw-translate-x) var(--tw-translate-y);\n      }\n    }\n  }\n  .data-\\[placeholder\\]\\:text-muted-foreground {\n    &[data-placeholder] {\n      color: var(--muted-foreground);\n    }\n  }\n  .data-\\[selected\\=true\\]\\:bg-accent {\n    &[data-selected=\"true\"] {\n      background-color: var(--accent);\n    }\n  }\n  .data-\\[selected\\=true\\]\\:text-accent-foreground {\n    &[data-selected=\"true\"] {\n      color: var(--accent-foreground);\n    }\n  }\n  .data-\\[side\\=bottom\\]\\:translate-y-1 {\n    &[data-side=\"bottom\"] {\n      --tw-translate-y: calc(var(--spacing) * 1);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .data-\\[side\\=bottom\\]\\:slide-in-from-top-2 {\n    &[data-side=\"bottom\"] {\n      --tw-enter-translate-y: calc(2*var(--spacing)*-1);\n    }\n  }\n  .data-\\[side\\=left\\]\\:-translate-x-1 {\n    &[data-side=\"left\"] {\n      --tw-translate-x: calc(var(--spacing) * -1);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .data-\\[side\\=left\\]\\:slide-in-from-right-2 {\n    &[data-side=\"left\"] {\n      --tw-enter-translate-x: calc(2*var(--spacing));\n    }\n  }\n  .data-\\[side\\=right\\]\\:translate-x-1 {\n    &[data-side=\"right\"] {\n      --tw-translate-x: calc(var(--spacing) * 1);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .data-\\[side\\=right\\]\\:slide-in-from-left-2 {\n    &[data-side=\"right\"] {\n      --tw-enter-translate-x: calc(2*var(--spacing)*-1);\n    }\n  }\n  .data-\\[side\\=top\\]\\:-translate-y-1 {\n    &[data-side=\"top\"] {\n      --tw-translate-y: calc(var(--spacing) * -1);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .data-\\[side\\=top\\]\\:slide-in-from-bottom-2 {\n    &[data-side=\"top\"] {\n      --tw-enter-translate-y: calc(2*var(--spacing));\n    }\n  }\n  .data-\\[size\\=default\\]\\:h-9 {\n    &[data-size=\"default\"] {\n      height: calc(var(--spacing) * 9);\n    }\n  }\n  .data-\\[size\\=sm\\]\\:h-8 {\n    &[data-size=\"sm\"] {\n      height: calc(var(--spacing) * 8);\n    }\n  }\n  .\\*\\:data-\\[slot\\=alert-description\\]\\:text-destructive\\/90 {\n    :is(& > *) {\n      &[data-slot=\"alert-description\"] {\n        color: var(--destructive);\n        @supports (color: color-mix(in lab, red, red)) {\n          color: color-mix(in oklab, var(--destructive) 90%, transparent);\n        }\n      }\n    }\n  }\n  .\\*\\*\\:data-\\[slot\\=command-input-wrapper\\]\\:h-12 {\n    :is(& *) {\n      &[data-slot=\"command-input-wrapper\"] {\n        height: calc(var(--spacing) * 12);\n      }\n    }\n  }\n  .\\*\\*\\:data-\\[slot\\=navigation-menu-link\\]\\:focus\\:ring-0 {\n    :is(& *) {\n      &[data-slot=\"navigation-menu-link\"] {\n        &:focus {\n          --tw-ring-shadow: var(--tw-ring-inset,) 0 0 0 calc(0px + var(--tw-ring-offset-width)) var(--tw-ring-color, currentcolor);\n          box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n        }\n      }\n    }\n  }\n  .\\*\\*\\:data-\\[slot\\=navigation-menu-link\\]\\:focus\\:outline-none {\n    :is(& *) {\n      &[data-slot=\"navigation-menu-link\"] {\n        &:focus {\n          --tw-outline-style: none;\n          outline-style: none;\n        }\n      }\n    }\n  }\n  .\\*\\:data-\\[slot\\=select-value\\]\\:line-clamp-1 {\n    :is(& > *) {\n      &[data-slot=\"select-value\"] {\n        overflow: hidden;\n        display: -webkit-box;\n        -webkit-box-orient: vertical;\n        -webkit-line-clamp: 1;\n      }\n    }\n  }\n  .\\*\\:data-\\[slot\\=select-value\\]\\:flex {\n    :is(& > *) {\n      &[data-slot=\"select-value\"] {\n        display: flex;\n      }\n    }\n  }\n  .\\*\\:data-\\[slot\\=select-value\\]\\:items-center {\n    :is(& > *) {\n      &[data-slot=\"select-value\"] {\n        align-items: center;\n      }\n    }\n  }\n  .\\*\\:data-\\[slot\\=select-value\\]\\:gap-2 {\n    :is(& > *) {\n      &[data-slot=\"select-value\"] {\n        gap: calc(var(--spacing) * 2);\n      }\n    }\n  }\n  .data-\\[state\\=active\\]\\:bg-background {\n    &[data-state=\"active\"] {\n      background-color: var(--background);\n    }\n  }\n  .data-\\[state\\=active\\]\\:shadow-sm {\n    &[data-state=\"active\"] {\n      --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .data-\\[state\\=checked\\]\\:translate-x-\\[calc\\(100\\%-2px\\)\\] {\n    &[data-state=\"checked\"] {\n      --tw-translate-x: calc(100% - 2px);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .data-\\[state\\=checked\\]\\:border-primary {\n    &[data-state=\"checked\"] {\n      border-color: var(--primary);\n    }\n  }\n  .data-\\[state\\=checked\\]\\:bg-primary {\n    &[data-state=\"checked\"] {\n      background-color: var(--primary);\n    }\n  }\n  .data-\\[state\\=checked\\]\\:text-primary-foreground {\n    &[data-state=\"checked\"] {\n      color: var(--primary-foreground);\n    }\n  }\n  .data-\\[state\\=closed\\]\\:animate-accordion-up {\n    &[data-state=\"closed\"] {\n      animation: accordion-up 0.2s ease-out;\n    }\n  }\n  .data-\\[state\\=closed\\]\\:animate-out {\n    &[data-state=\"closed\"] {\n      animation: exit var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease);\n    }\n  }\n  .data-\\[state\\=closed\\]\\:duration-300 {\n    &[data-state=\"closed\"] {\n      --tw-duration: 300ms;\n      transition-duration: 300ms;\n    }\n  }\n  .data-\\[state\\=closed\\]\\:fade-out-0 {\n    &[data-state=\"closed\"] {\n      --tw-exit-opacity: calc(0/100);\n      --tw-exit-opacity: 0;\n    }\n  }\n  .data-\\[state\\=closed\\]\\:zoom-out-95 {\n    &[data-state=\"closed\"] {\n      --tw-exit-scale: calc(95*1%);\n      --tw-exit-scale: .95;\n    }\n  }\n  .data-\\[state\\=closed\\]\\:slide-out-to-bottom {\n    &[data-state=\"closed\"] {\n      --tw-exit-translate-y: 100%;\n    }\n  }\n  .data-\\[state\\=closed\\]\\:slide-out-to-left {\n    &[data-state=\"closed\"] {\n      --tw-exit-translate-x: -100%;\n    }\n  }\n  .data-\\[state\\=closed\\]\\:slide-out-to-right {\n    &[data-state=\"closed\"] {\n      --tw-exit-translate-x: 100%;\n    }\n  }\n  .data-\\[state\\=closed\\]\\:slide-out-to-top {\n    &[data-state=\"closed\"] {\n      --tw-exit-translate-y: -100%;\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:data-\\[state\\=closed\\]\\:animate-out {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      &[data-state=\"closed\"] {\n        animation: exit var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease);\n      }\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:data-\\[state\\=closed\\]\\:fade-out-0 {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      &[data-state=\"closed\"] {\n        --tw-exit-opacity: calc(0/100);\n        --tw-exit-opacity: 0;\n      }\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:data-\\[state\\=closed\\]\\:zoom-out-95 {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      &[data-state=\"closed\"] {\n        --tw-exit-scale: calc(95*1%);\n        --tw-exit-scale: .95;\n      }\n    }\n  }\n  .data-\\[state\\=hidden\\]\\:animate-out {\n    &[data-state=\"hidden\"] {\n      animation: exit var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease);\n    }\n  }\n  .data-\\[state\\=hidden\\]\\:fade-out {\n    &[data-state=\"hidden\"] {\n      --tw-exit-opacity: 0;\n    }\n  }\n  .data-\\[state\\=on\\]\\:bg-accent {\n    &[data-state=\"on\"] {\n      background-color: var(--accent);\n    }\n  }\n  .data-\\[state\\=on\\]\\:text-accent-foreground {\n    &[data-state=\"on\"] {\n      color: var(--accent-foreground);\n    }\n  }\n  .data-\\[state\\=open\\]\\:animate-accordion-down {\n    &[data-state=\"open\"] {\n      animation: accordion-down 0.2s ease-out;\n    }\n  }\n  .data-\\[state\\=open\\]\\:animate-in {\n    &[data-state=\"open\"] {\n      animation: enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease);\n    }\n  }\n  .data-\\[state\\=open\\]\\:bg-accent {\n    &[data-state=\"open\"] {\n      background-color: var(--accent);\n    }\n  }\n  .data-\\[state\\=open\\]\\:bg-accent\\/50 {\n    &[data-state=\"open\"] {\n      background-color: var(--accent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--accent) 50%, transparent);\n      }\n    }\n  }\n  .data-\\[state\\=open\\]\\:bg-secondary {\n    &[data-state=\"open\"] {\n      background-color: var(--secondary);\n    }\n  }\n  .data-\\[state\\=open\\]\\:bg-transparent {\n    &[data-state=\"open\"] {\n      background-color: transparent;\n    }\n  }\n  .data-\\[state\\=open\\]\\:text-accent-foreground {\n    &[data-state=\"open\"] {\n      color: var(--accent-foreground);\n    }\n  }\n  .data-\\[state\\=open\\]\\:text-muted-foreground {\n    &[data-state=\"open\"] {\n      color: var(--muted-foreground);\n    }\n  }\n  .data-\\[state\\=open\\]\\:opacity-100 {\n    &[data-state=\"open\"] {\n      opacity: 100%;\n    }\n  }\n  .data-\\[state\\=open\\]\\:duration-500 {\n    &[data-state=\"open\"] {\n      --tw-duration: 500ms;\n      transition-duration: 500ms;\n    }\n  }\n  .data-\\[state\\=open\\]\\:fade-in-0 {\n    &[data-state=\"open\"] {\n      --tw-enter-opacity: calc(0/100);\n      --tw-enter-opacity: 0;\n    }\n  }\n  .data-\\[state\\=open\\]\\:zoom-in-90 {\n    &[data-state=\"open\"] {\n      --tw-enter-scale: calc(90*1%);\n      --tw-enter-scale: .9;\n    }\n  }\n  .data-\\[state\\=open\\]\\:zoom-in-95 {\n    &[data-state=\"open\"] {\n      --tw-enter-scale: calc(95*1%);\n      --tw-enter-scale: .95;\n    }\n  }\n  .data-\\[state\\=open\\]\\:slide-in-from-bottom {\n    &[data-state=\"open\"] {\n      --tw-enter-translate-y: 100%;\n    }\n  }\n  .data-\\[state\\=open\\]\\:slide-in-from-left {\n    &[data-state=\"open\"] {\n      --tw-enter-translate-x: -100%;\n    }\n  }\n  .data-\\[state\\=open\\]\\:slide-in-from-right {\n    &[data-state=\"open\"] {\n      --tw-enter-translate-x: 100%;\n    }\n  }\n  .data-\\[state\\=open\\]\\:slide-in-from-top {\n    &[data-state=\"open\"] {\n      --tw-enter-translate-y: -100%;\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:data-\\[state\\=open\\]\\:animate-in {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      &[data-state=\"open\"] {\n        animation: enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease);\n      }\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:data-\\[state\\=open\\]\\:fade-in-0 {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      &[data-state=\"open\"] {\n        --tw-enter-opacity: calc(0/100);\n        --tw-enter-opacity: 0;\n      }\n    }\n  }\n  .group-data-\\[viewport\\=false\\]\\/navigation-menu\\:data-\\[state\\=open\\]\\:zoom-in-95 {\n    &:is(:where(.group\\/navigation-menu)[data-viewport=\"false\"] *) {\n      &[data-state=\"open\"] {\n        --tw-enter-scale: calc(95*1%);\n        --tw-enter-scale: .95;\n      }\n    }\n  }\n  .data-\\[state\\=open\\]\\:hover\\:bg-accent {\n    &[data-state=\"open\"] {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--accent);\n        }\n      }\n    }\n  }\n  .data-\\[state\\=open\\]\\:hover\\:bg-sidebar-accent {\n    &[data-state=\"open\"] {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--sidebar-accent);\n        }\n      }\n    }\n  }\n  .data-\\[state\\=open\\]\\:hover\\:text-sidebar-accent-foreground {\n    &[data-state=\"open\"] {\n      &:hover {\n        @media (hover: hover) {\n          color: var(--sidebar-accent-foreground);\n        }\n      }\n    }\n  }\n  .data-\\[state\\=open\\]\\:focus\\:bg-accent {\n    &[data-state=\"open\"] {\n      &:focus {\n        background-color: var(--accent);\n      }\n    }\n  }\n  .data-\\[state\\=selected\\]\\:bg-muted {\n    &[data-state=\"selected\"] {\n      background-color: var(--muted);\n    }\n  }\n  .data-\\[state\\=unchecked\\]\\:translate-x-0 {\n    &[data-state=\"unchecked\"] {\n      --tw-translate-x: calc(var(--spacing) * 0);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .data-\\[state\\=unchecked\\]\\:bg-input {\n    &[data-state=\"unchecked\"] {\n      background-color: var(--input);\n    }\n  }\n  .data-\\[state\\=visible\\]\\:animate-in {\n    &[data-state=\"visible\"] {\n      animation: enter var(--tw-animation-duration,var(--tw-duration,.15s))var(--tw-ease,ease);\n    }\n  }\n  .data-\\[state\\=visible\\]\\:fade-in {\n    &[data-state=\"visible\"] {\n      --tw-enter-opacity: 0;\n    }\n  }\n  .data-\\[variant\\=destructive\\]\\:text-destructive {\n    &[data-variant=\"destructive\"] {\n      color: var(--destructive);\n    }\n  }\n  .data-\\[variant\\=destructive\\]\\:focus\\:bg-destructive\\/10 {\n    &[data-variant=\"destructive\"] {\n      &:focus {\n        background-color: var(--destructive);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--destructive) 10%, transparent);\n        }\n      }\n    }\n  }\n  .data-\\[variant\\=destructive\\]\\:focus\\:text-destructive {\n    &[data-variant=\"destructive\"] {\n      &:focus {\n        color: var(--destructive);\n      }\n    }\n  }\n  .data-\\[variant\\=outline\\]\\:border-l-0 {\n    &[data-variant=\"outline\"] {\n      border-left-style: var(--tw-border-style);\n      border-left-width: 0px;\n    }\n  }\n  .data-\\[variant\\=outline\\]\\:shadow-xs {\n    &[data-variant=\"outline\"] {\n      --tw-shadow: 0 1px 2px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.05));\n      box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n    }\n  }\n  .data-\\[variant\\=outline\\]\\:first\\:border-l {\n    &[data-variant=\"outline\"] {\n      &:first-child {\n        border-left-style: var(--tw-border-style);\n        border-left-width: 1px;\n      }\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=bottom\\]\\:inset-x-0 {\n    &[data-vaul-drawer-direction=\"bottom\"] {\n      inset-inline: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=bottom\\]\\:bottom-0 {\n    &[data-vaul-drawer-direction=\"bottom\"] {\n      bottom: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=bottom\\]\\:mt-24 {\n    &[data-vaul-drawer-direction=\"bottom\"] {\n      margin-top: calc(var(--spacing) * 24);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=bottom\\]\\:max-h-\\[80vh\\] {\n    &[data-vaul-drawer-direction=\"bottom\"] {\n      max-height: 80vh;\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=bottom\\]\\:rounded-t-lg {\n    &[data-vaul-drawer-direction=\"bottom\"] {\n      border-top-left-radius: var(--radius);\n      border-top-right-radius: var(--radius);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=bottom\\]\\:border-t {\n    &[data-vaul-drawer-direction=\"bottom\"] {\n      border-top-style: var(--tw-border-style);\n      border-top-width: 1px;\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=left\\]\\:inset-y-0 {\n    &[data-vaul-drawer-direction=\"left\"] {\n      inset-block: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=left\\]\\:left-0 {\n    &[data-vaul-drawer-direction=\"left\"] {\n      left: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=left\\]\\:w-3\\/4 {\n    &[data-vaul-drawer-direction=\"left\"] {\n      width: calc(3/4 * 100%);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=left\\]\\:border-r {\n    &[data-vaul-drawer-direction=\"left\"] {\n      border-right-style: var(--tw-border-style);\n      border-right-width: 1px;\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=right\\]\\:inset-y-0 {\n    &[data-vaul-drawer-direction=\"right\"] {\n      inset-block: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=right\\]\\:right-0 {\n    &[data-vaul-drawer-direction=\"right\"] {\n      right: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=right\\]\\:w-3\\/4 {\n    &[data-vaul-drawer-direction=\"right\"] {\n      width: calc(3/4 * 100%);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=right\\]\\:border-l {\n    &[data-vaul-drawer-direction=\"right\"] {\n      border-left-style: var(--tw-border-style);\n      border-left-width: 1px;\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=top\\]\\:inset-x-0 {\n    &[data-vaul-drawer-direction=\"top\"] {\n      inset-inline: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=top\\]\\:top-0 {\n    &[data-vaul-drawer-direction=\"top\"] {\n      top: calc(var(--spacing) * 0);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=top\\]\\:mb-24 {\n    &[data-vaul-drawer-direction=\"top\"] {\n      margin-bottom: calc(var(--spacing) * 24);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=top\\]\\:max-h-\\[80vh\\] {\n    &[data-vaul-drawer-direction=\"top\"] {\n      max-height: 80vh;\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=top\\]\\:rounded-b-lg {\n    &[data-vaul-drawer-direction=\"top\"] {\n      border-bottom-right-radius: var(--radius);\n      border-bottom-left-radius: var(--radius);\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=top\\]\\:border-b {\n    &[data-vaul-drawer-direction=\"top\"] {\n      border-bottom-style: var(--tw-border-style);\n      border-bottom-width: 1px;\n    }\n  }\n  .supports-\\[backdrop-filter\\]\\:bg-background\\/10 {\n    @supports (backdrop-filter: var(--tw)) {\n      background-color: var(--background);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--background) 10%, transparent);\n      }\n    }\n  }\n  .sm\\:block {\n    @media (width >= 40rem) {\n      display: block;\n    }\n  }\n  .sm\\:flex {\n    @media (width >= 40rem) {\n      display: flex;\n    }\n  }\n  .sm\\:hidden {\n    @media (width >= 40rem) {\n      display: none;\n    }\n  }\n  .sm\\:h-6 {\n    @media (width >= 40rem) {\n      height: calc(var(--spacing) * 6);\n    }\n  }\n  .sm\\:h-7 {\n    @media (width >= 40rem) {\n      height: calc(var(--spacing) * 7);\n    }\n  }\n  .sm\\:w-6 {\n    @media (width >= 40rem) {\n      width: calc(var(--spacing) * 6);\n    }\n  }\n  .sm\\:w-7 {\n    @media (width >= 40rem) {\n      width: calc(var(--spacing) * 7);\n    }\n  }\n  .sm\\:max-w-lg {\n    @media (width >= 40rem) {\n      max-width: var(--container-lg);\n    }\n  }\n  .sm\\:max-w-sm {\n    @media (width >= 40rem) {\n      max-width: var(--container-sm);\n    }\n  }\n  .sm\\:flex-1 {\n    @media (width >= 40rem) {\n      flex: 1;\n    }\n  }\n  .sm\\:grid-cols-2 {\n    @media (width >= 40rem) {\n      grid-template-columns: repeat(2, minmax(0, 1fr));\n    }\n  }\n  .sm\\:grid-cols-4 {\n    @media (width >= 40rem) {\n      grid-template-columns: repeat(4, minmax(0, 1fr));\n    }\n  }\n  .sm\\:flex-row {\n    @media (width >= 40rem) {\n      flex-direction: row;\n    }\n  }\n  .sm\\:items-center {\n    @media (width >= 40rem) {\n      align-items: center;\n    }\n  }\n  .sm\\:justify-between {\n    @media (width >= 40rem) {\n      justify-content: space-between;\n    }\n  }\n  .sm\\:justify-end {\n    @media (width >= 40rem) {\n      justify-content: flex-end;\n    }\n  }\n  .sm\\:gap-2\\.5 {\n    @media (width >= 40rem) {\n      gap: calc(var(--spacing) * 2.5);\n    }\n  }\n  .sm\\:gap-3 {\n    @media (width >= 40rem) {\n      gap: calc(var(--spacing) * 3);\n    }\n  }\n  .sm\\:border-neutral-700 {\n    @media (width >= 40rem) {\n      border-color: var(--color-neutral-700);\n    }\n  }\n  .sm\\:bg-neutral-900\\/50 {\n    @media (width >= 40rem) {\n      background-color: color-mix(in srgb, oklch(20.5% 0 0) 50%, transparent);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--color-neutral-900) 50%, transparent);\n      }\n    }\n  }\n  .sm\\:px-4 {\n    @media (width >= 40rem) {\n      padding-inline: calc(var(--spacing) * 4);\n    }\n  }\n  .sm\\:px-6 {\n    @media (width >= 40rem) {\n      padding-inline: calc(var(--spacing) * 6);\n    }\n  }\n  .sm\\:px-10 {\n    @media (width >= 40rem) {\n      padding-inline: calc(var(--spacing) * 10);\n    }\n  }\n  .sm\\:px-12 {\n    @media (width >= 40rem) {\n      padding-inline: calc(var(--spacing) * 12);\n    }\n  }\n  .sm\\:py-5 {\n    @media (width >= 40rem) {\n      padding-block: calc(var(--spacing) * 5);\n    }\n  }\n  .sm\\:pr-2\\.5 {\n    @media (width >= 40rem) {\n      padding-right: calc(var(--spacing) * 2.5);\n    }\n  }\n  .sm\\:pl-2\\.5 {\n    @media (width >= 40rem) {\n      padding-left: calc(var(--spacing) * 2.5);\n    }\n  }\n  .sm\\:text-left {\n    @media (width >= 40rem) {\n      text-align: left;\n    }\n  }\n  .sm\\:text-xl {\n    @media (width >= 40rem) {\n      font-size: var(--text-xl);\n      line-height: var(--tw-leading, var(--text-xl--line-height));\n    }\n  }\n  .sm\\:hover\\:bg-neutral-800\\/50 {\n    @media (width >= 40rem) {\n      &:hover {\n        @media (hover: hover) {\n          background-color: color-mix(in srgb, oklch(26.9% 0 0) 50%, transparent);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--color-neutral-800) 50%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=left\\]\\:sm\\:max-w-sm {\n    &[data-vaul-drawer-direction=\"left\"] {\n      @media (width >= 40rem) {\n        max-width: var(--container-sm);\n      }\n    }\n  }\n  .data-\\[vaul-drawer-direction\\=right\\]\\:sm\\:max-w-sm {\n    &[data-vaul-drawer-direction=\"right\"] {\n      @media (width >= 40rem) {\n        max-width: var(--container-sm);\n      }\n    }\n  }\n  .md\\:absolute {\n    @media (width >= 48rem) {\n      position: absolute;\n    }\n  }\n  .md\\:col-span-2 {\n    @media (width >= 48rem) {\n      grid-column: span 2 / span 2;\n    }\n  }\n  .md\\:block {\n    @media (width >= 48rem) {\n      display: block;\n    }\n  }\n  .md\\:flex {\n    @media (width >= 48rem) {\n      display: flex;\n    }\n  }\n  .md\\:inline-flex {\n    @media (width >= 48rem) {\n      display: inline-flex;\n    }\n  }\n  .md\\:w-\\[var\\(--radix-navigation-menu-viewport-width\\)\\] {\n    @media (width >= 48rem) {\n      width: var(--radix-navigation-menu-viewport-width);\n    }\n  }\n  .md\\:w-auto {\n    @media (width >= 48rem) {\n      width: auto;\n    }\n  }\n  .md\\:grid-cols-2 {\n    @media (width >= 48rem) {\n      grid-template-columns: repeat(2, minmax(0, 1fr));\n    }\n  }\n  .md\\:grid-cols-3 {\n    @media (width >= 48rem) {\n      grid-template-columns: repeat(3, minmax(0, 1fr));\n    }\n  }\n  .md\\:grid-cols-5 {\n    @media (width >= 48rem) {\n      grid-template-columns: repeat(5, minmax(0, 1fr));\n    }\n  }\n  .md\\:gap-12 {\n    @media (width >= 48rem) {\n      gap: calc(var(--spacing) * 12);\n    }\n  }\n  .md\\:text-4xl {\n    @media (width >= 48rem) {\n      font-size: var(--text-4xl);\n      line-height: var(--tw-leading, var(--text-4xl--line-height));\n    }\n  }\n  .md\\:text-5xl {\n    @media (width >= 48rem) {\n      font-size: var(--text-5xl);\n      line-height: var(--tw-leading, var(--text-5xl--line-height));\n    }\n  }\n  .md\\:text-6xl {\n    @media (width >= 48rem) {\n      font-size: var(--text-6xl);\n      line-height: var(--tw-leading, var(--text-6xl--line-height));\n    }\n  }\n  .md\\:text-7xl {\n    @media (width >= 48rem) {\n      font-size: var(--text-7xl);\n      line-height: var(--tw-leading, var(--text-7xl--line-height));\n    }\n  }\n  .md\\:text-sm {\n    @media (width >= 48rem) {\n      font-size: var(--text-sm);\n      line-height: var(--tw-leading, var(--text-sm--line-height));\n    }\n  }\n  .md\\:text-xl {\n    @media (width >= 48rem) {\n      font-size: var(--text-xl);\n      line-height: var(--tw-leading, var(--text-xl--line-height));\n    }\n  }\n  .md\\:opacity-0 {\n    @media (width >= 48rem) {\n      opacity: 0%;\n    }\n  }\n  .md\\:peer-data-\\[variant\\=inset\\]\\:m-2 {\n    @media (width >= 48rem) {\n      &:is(:where(.peer)[data-variant=\"inset\"] ~ *) {\n        margin: calc(var(--spacing) * 2);\n      }\n    }\n  }\n  .md\\:peer-data-\\[variant\\=inset\\]\\:ml-0 {\n    @media (width >= 48rem) {\n      &:is(:where(.peer)[data-variant=\"inset\"] ~ *) {\n        margin-left: calc(var(--spacing) * 0);\n      }\n    }\n  }\n  .md\\:peer-data-\\[variant\\=inset\\]\\:rounded-xl {\n    @media (width >= 48rem) {\n      &:is(:where(.peer)[data-variant=\"inset\"] ~ *) {\n        border-radius: calc(var(--radius) + 4px);\n      }\n    }\n  }\n  .md\\:peer-data-\\[variant\\=inset\\]\\:shadow-sm {\n    @media (width >= 48rem) {\n      &:is(:where(.peer)[data-variant=\"inset\"] ~ *) {\n        --tw-shadow: 0 1px 3px 0 var(--tw-shadow-color, rgb(0 0 0 / 0.1)), 0 1px 2px -1px var(--tw-shadow-color, rgb(0 0 0 / 0.1));\n        box-shadow: var(--tw-inset-shadow), var(--tw-inset-ring-shadow), var(--tw-ring-offset-shadow), var(--tw-ring-shadow), var(--tw-shadow);\n      }\n    }\n  }\n  .md\\:peer-data-\\[variant\\=inset\\]\\:peer-data-\\[state\\=collapsed\\]\\:ml-2 {\n    @media (width >= 48rem) {\n      &:is(:where(.peer)[data-variant=\"inset\"] ~ *) {\n        &:is(:where(.peer)[data-state=\"collapsed\"] ~ *) {\n          margin-left: calc(var(--spacing) * 2);\n        }\n      }\n    }\n  }\n  .md\\:after\\:hidden {\n    @media (width >= 48rem) {\n      &::after {\n        content: var(--tw-content);\n        display: none;\n      }\n    }\n  }\n  .lg\\:col-span-1 {\n    @media (width >= 64rem) {\n      grid-column: span 1 / span 1;\n    }\n  }\n  .lg\\:col-span-2 {\n    @media (width >= 64rem) {\n      grid-column: span 2 / span 2;\n    }\n  }\n  .lg\\:mx-0 {\n    @media (width >= 64rem) {\n      margin-inline: calc(var(--spacing) * 0);\n    }\n  }\n  .lg\\:mb-20 {\n    @media (width >= 64rem) {\n      margin-bottom: calc(var(--spacing) * 20);\n    }\n  }\n  .lg\\:block {\n    @media (width >= 64rem) {\n      display: block;\n    }\n  }\n  .lg\\:flex {\n    @media (width >= 64rem) {\n      display: flex;\n    }\n  }\n  .lg\\:hidden {\n    @media (width >= 64rem) {\n      display: none;\n    }\n  }\n  .lg\\:w-64 {\n    @media (width >= 64rem) {\n      width: calc(var(--spacing) * 64);\n    }\n  }\n  .lg\\:max-w-3xl {\n    @media (width >= 64rem) {\n      max-width: var(--container-3xl);\n    }\n  }\n  .lg\\:max-w-lg {\n    @media (width >= 64rem) {\n      max-width: var(--container-lg);\n    }\n  }\n  .lg\\:max-w-sm {\n    @media (width >= 64rem) {\n      max-width: var(--container-sm);\n    }\n  }\n  .lg\\:max-w-xl {\n    @media (width >= 64rem) {\n      max-width: var(--container-xl);\n    }\n  }\n  .lg\\:basis-1\\/2 {\n    @media (width >= 64rem) {\n      flex-basis: calc(1/2 * 100%);\n    }\n  }\n  .lg\\:basis-1\\/6 {\n    @media (width >= 64rem) {\n      flex-basis: calc(1/6 * 100%);\n    }\n  }\n  .lg\\:grid-cols-2 {\n    @media (width >= 64rem) {\n      grid-template-columns: repeat(2, minmax(0, 1fr));\n    }\n  }\n  .lg\\:grid-cols-3 {\n    @media (width >= 64rem) {\n      grid-template-columns: repeat(3, minmax(0, 1fr));\n    }\n  }\n  .lg\\:flex-row {\n    @media (width >= 64rem) {\n      flex-direction: row;\n    }\n  }\n  .lg\\:gap-16 {\n    @media (width >= 64rem) {\n      gap: calc(var(--spacing) * 16);\n    }\n  }\n  .lg\\:p-14 {\n    @media (width >= 64rem) {\n      padding: calc(var(--spacing) * 14);\n    }\n  }\n  .lg\\:px-6 {\n    @media (width >= 64rem) {\n      padding-inline: calc(var(--spacing) * 6);\n    }\n  }\n  .lg\\:px-8 {\n    @media (width >= 64rem) {\n      padding-inline: calc(var(--spacing) * 8);\n    }\n  }\n  .lg\\:px-16 {\n    @media (width >= 64rem) {\n      padding-inline: calc(var(--spacing) * 16);\n    }\n  }\n  .lg\\:py-8 {\n    @media (width >= 64rem) {\n      padding-block: calc(var(--spacing) * 8);\n    }\n  }\n  .lg\\:py-12 {\n    @media (width >= 64rem) {\n      padding-block: calc(var(--spacing) * 12);\n    }\n  }\n  .lg\\:py-16 {\n    @media (width >= 64rem) {\n      padding-block: calc(var(--spacing) * 16);\n    }\n  }\n  .lg\\:py-24 {\n    @media (width >= 64rem) {\n      padding-block: calc(var(--spacing) * 24);\n    }\n  }\n  .lg\\:py-32 {\n    @media (width >= 64rem) {\n      padding-block: calc(var(--spacing) * 32);\n    }\n  }\n  .lg\\:py-40 {\n    @media (width >= 64rem) {\n      padding-block: calc(var(--spacing) * 40);\n    }\n  }\n  .lg\\:pt-12 {\n    @media (width >= 64rem) {\n      padding-top: calc(var(--spacing) * 12);\n    }\n  }\n  .lg\\:pt-48 {\n    @media (width >= 64rem) {\n      padding-top: calc(var(--spacing) * 48);\n    }\n  }\n  .lg\\:pr-16 {\n    @media (width >= 64rem) {\n      padding-right: calc(var(--spacing) * 16);\n    }\n  }\n  .lg\\:pb-12 {\n    @media (width >= 64rem) {\n      padding-bottom: calc(var(--spacing) * 12);\n    }\n  }\n  .lg\\:pb-40 {\n    @media (width >= 64rem) {\n      padding-bottom: calc(var(--spacing) * 40);\n    }\n  }\n  .lg\\:pl-16 {\n    @media (width >= 64rem) {\n      padding-left: calc(var(--spacing) * 16);\n    }\n  }\n  .lg\\:text-5xl {\n    @media (width >= 64rem) {\n      font-size: var(--text-5xl);\n      line-height: var(--tw-leading, var(--text-5xl--line-height));\n    }\n  }\n  .lg\\:text-7xl {\n    @media (width >= 64rem) {\n      font-size: var(--text-7xl);\n      line-height: var(--tw-leading, var(--text-7xl--line-height));\n    }\n  }\n  .lg\\:text-\\[20rem\\] {\n    @media (width >= 64rem) {\n      font-size: 20rem;\n    }\n  }\n  .xl\\:px-24 {\n    @media (width >= 80rem) {\n      padding-inline: calc(var(--spacing) * 24);\n    }\n  }\n  .xl\\:text-\\[24rem\\] {\n    @media (width >= 80rem) {\n      font-size: 24rem;\n    }\n  }\n  .\\32 xl\\:hidden {\n    @media (width >= 96rem) {\n      display: none;\n    }\n  }\n  .dark\\:scale-0 {\n    &:is(.dark *) {\n      --tw-scale-x: 0%;\n      --tw-scale-y: 0%;\n      --tw-scale-z: 0%;\n      scale: var(--tw-scale-x) var(--tw-scale-y);\n    }\n  }\n  .dark\\:scale-100 {\n    &:is(.dark *) {\n      --tw-scale-x: 100%;\n      --tw-scale-y: 100%;\n      --tw-scale-z: 100%;\n      scale: var(--tw-scale-x) var(--tw-scale-y);\n    }\n  }\n  .dark\\:-rotate-90 {\n    &:is(.dark *) {\n      rotate: calc(90deg * -1);\n    }\n  }\n  .dark\\:rotate-0 {\n    &:is(.dark *) {\n      rotate: 0deg;\n    }\n  }\n  .dark\\:border-input {\n    &:is(.dark *) {\n      border-color: var(--input);\n    }\n  }\n  .dark\\:bg-destructive\\/60 {\n    &:is(.dark *) {\n      background-color: var(--destructive);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--destructive) 60%, transparent);\n      }\n    }\n  }\n  .dark\\:bg-input\\/30 {\n    &:is(.dark *) {\n      background-color: var(--input);\n      @supports (color: color-mix(in lab, red, red)) {\n        background-color: color-mix(in oklab, var(--input) 30%, transparent);\n      }\n    }\n  }\n  .dark\\:text-muted-foreground {\n    &:is(.dark *) {\n      color: var(--muted-foreground);\n    }\n  }\n  .dark\\:invert {\n    &:is(.dark *) {\n      --tw-invert: invert(100%);\n      filter: var(--tw-blur,) var(--tw-brightness,) var(--tw-contrast,) var(--tw-grayscale,) var(--tw-hue-rotate,) var(--tw-invert,) var(--tw-saturate,) var(--tw-sepia,) var(--tw-drop-shadow,);\n    }\n  }\n  .dark\\:prose-invert {\n    &:is(.dark *) {\n      --tw-prose-body: var(--tw-prose-invert-body);\n      --tw-prose-headings: var(--tw-prose-invert-headings);\n      --tw-prose-lead: var(--tw-prose-invert-lead);\n      --tw-prose-links: var(--tw-prose-invert-links);\n      --tw-prose-bold: var(--tw-prose-invert-bold);\n      --tw-prose-counters: var(--tw-prose-invert-counters);\n      --tw-prose-bullets: var(--tw-prose-invert-bullets);\n      --tw-prose-hr: var(--tw-prose-invert-hr);\n      --tw-prose-quotes: var(--tw-prose-invert-quotes);\n      --tw-prose-quote-borders: var(--tw-prose-invert-quote-borders);\n      --tw-prose-captions: var(--tw-prose-invert-captions);\n      --tw-prose-kbd: var(--tw-prose-invert-kbd);\n      --tw-prose-kbd-shadows: var(--tw-prose-invert-kbd-shadows);\n      --tw-prose-code: var(--tw-prose-invert-code);\n      --tw-prose-pre-code: var(--tw-prose-invert-pre-code);\n      --tw-prose-pre-bg: var(--tw-prose-invert-pre-bg);\n      --tw-prose-th-borders: var(--tw-prose-invert-th-borders);\n      --tw-prose-td-borders: var(--tw-prose-invert-td-borders);\n    }\n  }\n  .dark\\:hover\\:bg-accent\\/50 {\n    &:is(.dark *) {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--accent);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--accent) 50%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .dark\\:hover\\:bg-input\\/50 {\n    &:is(.dark *) {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--input);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--input) 50%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .dark\\:focus-visible\\:ring-destructive\\/40 {\n    &:is(.dark *) {\n      &:focus-visible {\n        --tw-ring-color: var(--destructive);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);\n        }\n      }\n    }\n  }\n  .dark\\:aria-invalid\\:ring-destructive\\/40 {\n    &:is(.dark *) {\n      &[aria-invalid=\"true\"] {\n        --tw-ring-color: var(--destructive);\n        @supports (color: color-mix(in lab, red, red)) {\n          --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);\n        }\n      }\n    }\n  }\n  .dark\\:data-\\[active\\=true\\]\\:aria-invalid\\:ring-destructive\\/40 {\n    &:is(.dark *) {\n      &[data-active=\"true\"] {\n        &[aria-invalid=\"true\"] {\n          --tw-ring-color: var(--destructive);\n          @supports (color: color-mix(in lab, red, red)) {\n            --tw-ring-color: color-mix(in oklab, var(--destructive) 40%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .dark\\:data-\\[state\\=active\\]\\:border-input {\n    &:is(.dark *) {\n      &[data-state=\"active\"] {\n        border-color: var(--input);\n      }\n    }\n  }\n  .dark\\:data-\\[state\\=active\\]\\:bg-input\\/30 {\n    &:is(.dark *) {\n      &[data-state=\"active\"] {\n        background-color: var(--input);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--input) 30%, transparent);\n        }\n      }\n    }\n  }\n  .dark\\:data-\\[state\\=active\\]\\:text-foreground {\n    &:is(.dark *) {\n      &[data-state=\"active\"] {\n        color: var(--foreground);\n      }\n    }\n  }\n  .dark\\:data-\\[state\\=checked\\]\\:bg-primary {\n    &:is(.dark *) {\n      &[data-state=\"checked\"] {\n        background-color: var(--primary);\n      }\n    }\n  }\n  .dark\\:data-\\[state\\=checked\\]\\:bg-primary-foreground {\n    &:is(.dark *) {\n      &[data-state=\"checked\"] {\n        background-color: var(--primary-foreground);\n      }\n    }\n  }\n  .dark\\:data-\\[state\\=unchecked\\]\\:bg-foreground {\n    &:is(.dark *) {\n      &[data-state=\"unchecked\"] {\n        background-color: var(--foreground);\n      }\n    }\n  }\n  .dark\\:data-\\[state\\=unchecked\\]\\:bg-input\\/80 {\n    &:is(.dark *) {\n      &[data-state=\"unchecked\"] {\n        background-color: var(--input);\n        @supports (color: color-mix(in lab, red, red)) {\n          background-color: color-mix(in oklab, var(--input) 80%, transparent);\n        }\n      }\n    }\n  }\n  .dark\\:data-\\[variant\\=destructive\\]\\:focus\\:bg-destructive\\/20 {\n    &:is(.dark *) {\n      &[data-variant=\"destructive\"] {\n        &:focus {\n          background-color: var(--destructive);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--destructive) 20%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .\\[\\&_\\.recharts-cartesian-axis-tick_text\\]\\:fill-muted-foreground {\n    & .recharts-cartesian-axis-tick text {\n      fill: var(--muted-foreground);\n    }\n  }\n  .\\[\\&_\\.recharts-cartesian-grid_line\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border\\/50 {\n    & .recharts-cartesian-grid line[stroke='#ccc'] {\n      stroke: var(--border);\n      @supports (color: color-mix(in lab, red, red)) {\n        stroke: color-mix(in oklab, var(--border) 50%, transparent);\n      }\n    }\n  }\n  .\\[\\&_\\.recharts-curve\\.recharts-tooltip-cursor\\]\\:stroke-border {\n    & .recharts-curve.recharts-tooltip-cursor {\n      stroke: var(--border);\n    }\n  }\n  .\\[\\&_\\.recharts-dot\\[stroke\\=\\'\\#fff\\'\\]\\]\\:stroke-transparent {\n    & .recharts-dot[stroke='#fff'] {\n      stroke: transparent;\n    }\n  }\n  .\\[\\&_\\.recharts-layer\\]\\:outline-hidden {\n    & .recharts-layer {\n      --tw-outline-style: none;\n      outline-style: none;\n      @media (forced-colors: active) {\n        outline: 2px solid transparent;\n        outline-offset: 2px;\n      }\n    }\n  }\n  .\\[\\&_\\.recharts-polar-grid_\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border {\n    & .recharts-polar-grid [stroke='#ccc'] {\n      stroke: var(--border);\n    }\n  }\n  .\\[\\&_\\.recharts-radial-bar-background-sector\\]\\:fill-muted {\n    & .recharts-radial-bar-background-sector {\n      fill: var(--muted);\n    }\n  }\n  .\\[\\&_\\.recharts-rectangle\\.recharts-tooltip-cursor\\]\\:fill-muted {\n    & .recharts-rectangle.recharts-tooltip-cursor {\n      fill: var(--muted);\n    }\n  }\n  .\\[\\&_\\.recharts-reference-line_\\[stroke\\=\\'\\#ccc\\'\\]\\]\\:stroke-border {\n    & .recharts-reference-line [stroke='#ccc'] {\n      stroke: var(--border);\n    }\n  }\n  .\\[\\&_\\.recharts-sector\\]\\:outline-hidden {\n    & .recharts-sector {\n      --tw-outline-style: none;\n      outline-style: none;\n      @media (forced-colors: active) {\n        outline: 2px solid transparent;\n        outline-offset: 2px;\n      }\n    }\n  }\n  .\\[\\&_\\.recharts-sector\\[stroke\\=\\'\\#fff\\'\\]\\]\\:stroke-transparent {\n    & .recharts-sector[stroke='#fff'] {\n      stroke: transparent;\n    }\n  }\n  .\\[\\&_\\.recharts-surface\\]\\:outline-hidden {\n    & .recharts-surface {\n      --tw-outline-style: none;\n      outline-style: none;\n      @media (forced-colors: active) {\n        outline: 2px solid transparent;\n        outline-offset: 2px;\n      }\n    }\n  }\n  .\\[\\&_\\[cmdk-group-heading\\]\\]\\:px-2 {\n    & [cmdk-group-heading] {\n      padding-inline: calc(var(--spacing) * 2);\n    }\n  }\n  .\\[\\&_\\[cmdk-group-heading\\]\\]\\:py-1\\.5 {\n    & [cmdk-group-heading] {\n      padding-block: calc(var(--spacing) * 1.5);\n    }\n  }\n  .\\[\\&_\\[cmdk-group-heading\\]\\]\\:text-xs {\n    & [cmdk-group-heading] {\n      font-size: var(--text-xs);\n      line-height: var(--tw-leading, var(--text-xs--line-height));\n    }\n  }\n  .\\[\\&_\\[cmdk-group-heading\\]\\]\\:font-medium {\n    & [cmdk-group-heading] {\n      --tw-font-weight: var(--font-weight-medium);\n      font-weight: var(--font-weight-medium);\n    }\n  }\n  .\\[\\&_\\[cmdk-group-heading\\]\\]\\:text-muted-foreground {\n    & [cmdk-group-heading] {\n      color: var(--muted-foreground);\n    }\n  }\n  .\\[\\&_\\[cmdk-group\\]\\]\\:px-2 {\n    & [cmdk-group] {\n      padding-inline: calc(var(--spacing) * 2);\n    }\n  }\n  .\\[\\&_\\[cmdk-group\\]\\:not\\(\\[hidden\\]\\)_\\~\\[cmdk-group\\]\\]\\:pt-0 {\n    & [cmdk-group]:not([hidden]) ~[cmdk-group] {\n      padding-top: calc(var(--spacing) * 0);\n    }\n  }\n  .\\[\\&_\\[cmdk-input-wrapper\\]_svg\\]\\:h-5 {\n    & [cmdk-input-wrapper] svg {\n      height: calc(var(--spacing) * 5);\n    }\n  }\n  .\\[\\&_\\[cmdk-input-wrapper\\]_svg\\]\\:w-5 {\n    & [cmdk-input-wrapper] svg {\n      width: calc(var(--spacing) * 5);\n    }\n  }\n  .\\[\\&_\\[cmdk-input\\]\\]\\:h-12 {\n    & [cmdk-input] {\n      height: calc(var(--spacing) * 12);\n    }\n  }\n  .\\[\\&_\\[cmdk-item\\]\\]\\:px-2 {\n    & [cmdk-item] {\n      padding-inline: calc(var(--spacing) * 2);\n    }\n  }\n  .\\[\\&_\\[cmdk-item\\]\\]\\:py-3 {\n    & [cmdk-item] {\n      padding-block: calc(var(--spacing) * 3);\n    }\n  }\n  .\\[\\&_\\[cmdk-item\\]_svg\\]\\:h-5 {\n    & [cmdk-item] svg {\n      height: calc(var(--spacing) * 5);\n    }\n  }\n  .\\[\\&_\\[cmdk-item\\]_svg\\]\\:w-5 {\n    & [cmdk-item] svg {\n      width: calc(var(--spacing) * 5);\n    }\n  }\n  .\\[\\&_p\\]\\:leading-relaxed {\n    & p {\n      --tw-leading: var(--leading-relaxed);\n      line-height: var(--leading-relaxed);\n    }\n  }\n  .\\[\\&_svg\\]\\:pointer-events-none {\n    & svg {\n      pointer-events: none;\n    }\n  }\n  .\\[\\&_svg\\]\\:shrink-0 {\n    & svg {\n      flex-shrink: 0;\n    }\n  }\n  .\\[\\&_svg\\:not\\(\\[class\\*\\=\\'size-\\'\\]\\)\\]\\:size-4 {\n    & svg:not([class*='size-']) {\n      width: calc(var(--spacing) * 4);\n      height: calc(var(--spacing) * 4);\n    }\n  }\n  .\\[\\&_svg\\:not\\(\\[class\\*\\=\\'text-\\'\\]\\)\\]\\:text-muted-foreground {\n    & svg:not([class*='text-']) {\n      color: var(--muted-foreground);\n    }\n  }\n  .\\[\\&_tr\\]\\:border-b {\n    & tr {\n      border-bottom-style: var(--tw-border-style);\n      border-bottom-width: 1px;\n    }\n  }\n  .\\[\\&_tr\\:last-child\\]\\:border-0 {\n    & tr:last-child {\n      border-style: var(--tw-border-style);\n      border-width: 0px;\n    }\n  }\n  .\\[\\&\\:has\\(\\>\\.day-range-end\\)\\]\\:rounded-r-md {\n    &:has(>.day-range-end) {\n      border-top-right-radius: calc(var(--radius) - 2px);\n      border-bottom-right-radius: calc(var(--radius) - 2px);\n    }\n  }\n  .\\[\\&\\:has\\(\\>\\.day-range-start\\)\\]\\:rounded-l-md {\n    &:has(>.day-range-start) {\n      border-top-left-radius: calc(var(--radius) - 2px);\n      border-bottom-left-radius: calc(var(--radius) - 2px);\n    }\n  }\n  .\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:rounded-md {\n    &:has([aria-selected]) {\n      border-radius: calc(var(--radius) - 2px);\n    }\n  }\n  .\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:bg-accent {\n    &:has([aria-selected]) {\n      background-color: var(--accent);\n    }\n  }\n  .first\\:\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:rounded-l-md {\n    &:first-child {\n      &:has([aria-selected]) {\n        border-top-left-radius: calc(var(--radius) - 2px);\n        border-bottom-left-radius: calc(var(--radius) - 2px);\n      }\n    }\n  }\n  .last\\:\\[\\&\\:has\\(\\[aria-selected\\]\\)\\]\\:rounded-r-md {\n    &:last-child {\n      &:has([aria-selected]) {\n        border-top-right-radius: calc(var(--radius) - 2px);\n        border-bottom-right-radius: calc(var(--radius) - 2px);\n      }\n    }\n  }\n  .\\[\\&\\:has\\(\\[aria-selected\\]\\.day-range-end\\)\\]\\:rounded-r-md {\n    &:has([aria-selected].day-range-end) {\n      border-top-right-radius: calc(var(--radius) - 2px);\n      border-bottom-right-radius: calc(var(--radius) - 2px);\n    }\n  }\n  .\\[\\&\\:has\\(\\[role\\=checkbox\\]\\)\\]\\:pr-0 {\n    &:has([role=checkbox]) {\n      padding-right: calc(var(--spacing) * 0);\n    }\n  }\n  .\\[\\.border-b\\]\\:pb-6 {\n    &:is(.border-b) {\n      padding-bottom: calc(var(--spacing) * 6);\n    }\n  }\n  .\\[\\.border-t\\]\\:pt-6 {\n    &:is(.border-t) {\n      padding-top: calc(var(--spacing) * 6);\n    }\n  }\n  .\\*\\:\\[span\\]\\:last\\:flex {\n    :is(& > *) {\n      &:is(span) {\n        &:last-child {\n          display: flex;\n        }\n      }\n    }\n  }\n  .\\*\\:\\[span\\]\\:last\\:items-center {\n    :is(& > *) {\n      &:is(span) {\n        &:last-child {\n          align-items: center;\n        }\n      }\n    }\n  }\n  .\\*\\:\\[span\\]\\:last\\:gap-2 {\n    :is(& > *) {\n      &:is(span) {\n        &:last-child {\n          gap: calc(var(--spacing) * 2);\n        }\n      }\n    }\n  }\n  .data-\\[variant\\=destructive\\]\\:\\*\\:\\[svg\\]\\:\\!text-destructive {\n    &[data-variant=\"destructive\"] {\n      :is(& > *) {\n        &:is(svg) {\n          color: var(--destructive) !important;\n        }\n      }\n    }\n  }\n  .\\[\\&\\:not\\(\\:first-child\\)\\]\\:mt-6 {\n    &:not(:first-child) {\n      margin-top: calc(var(--spacing) * 6);\n    }\n  }\n  .\\[\\&\\>\\[role\\=checkbox\\]\\]\\:translate-y-\\[2px\\] {\n    &>[role=checkbox] {\n      --tw-translate-y: 2px;\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .\\[\\&\\>button\\]\\:hidden {\n    &>button {\n      display: none;\n    }\n  }\n  .\\[\\&\\>span\\:last-child\\]\\:truncate {\n    &>span:last-child {\n      overflow: hidden;\n      text-overflow: ellipsis;\n      white-space: nowrap;\n    }\n  }\n  .\\[\\&\\>svg\\]\\:pointer-events-none {\n    &>svg {\n      pointer-events: none;\n    }\n  }\n  .\\[\\&\\>svg\\]\\:size-3 {\n    &>svg {\n      width: calc(var(--spacing) * 3);\n      height: calc(var(--spacing) * 3);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:size-3\\.5 {\n    &>svg {\n      width: calc(var(--spacing) * 3.5);\n      height: calc(var(--spacing) * 3.5);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:size-4 {\n    &>svg {\n      width: calc(var(--spacing) * 4);\n      height: calc(var(--spacing) * 4);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:h-2\\.5 {\n    &>svg {\n      height: calc(var(--spacing) * 2.5);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:h-3 {\n    &>svg {\n      height: calc(var(--spacing) * 3);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:w-2\\.5 {\n    &>svg {\n      width: calc(var(--spacing) * 2.5);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:w-3 {\n    &>svg {\n      width: calc(var(--spacing) * 3);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:shrink-0 {\n    &>svg {\n      flex-shrink: 0;\n    }\n  }\n  .\\[\\&\\>svg\\]\\:translate-y-0\\.5 {\n    &>svg {\n      --tw-translate-y: calc(var(--spacing) * 0.5);\n      translate: var(--tw-translate-x) var(--tw-translate-y);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:text-current {\n    &>svg {\n      color: currentcolor;\n    }\n  }\n  .\\[\\&\\>svg\\]\\:text-muted-foreground {\n    &>svg {\n      color: var(--muted-foreground);\n    }\n  }\n  .\\[\\&\\>svg\\]\\:text-sidebar-accent-foreground {\n    &>svg {\n      color: var(--sidebar-accent-foreground);\n    }\n  }\n  .\\[\\&\\>tr\\]\\:last\\:border-b-0 {\n    &>tr {\n      &:last-child {\n        border-bottom-style: var(--tw-border-style);\n        border-bottom-width: 0px;\n      }\n    }\n  }\n  .\\[\\&\\[data-panel-group-direction\\=vertical\\]\\>div\\]\\:rotate-90 {\n    &[data-panel-group-direction=vertical]>div {\n      rotate: 90deg;\n    }\n  }\n  .\\[\\&\\[data-state\\=open\\]\\>svg\\]\\:rotate-180 {\n    &[data-state=open]>svg {\n      rotate: 180deg;\n    }\n  }\n  .\\[\\[data-side\\=left\\]\\[data-collapsible\\=offcanvas\\]_\\&\\]\\:-right-2 {\n    [data-side=left][data-collapsible=offcanvas] & {\n      right: calc(var(--spacing) * -2);\n    }\n  }\n  .\\[\\[data-side\\=left\\]\\[data-state\\=collapsed\\]_\\&\\]\\:cursor-e-resize {\n    [data-side=left][data-state=collapsed] & {\n      cursor: e-resize;\n    }\n  }\n  .\\[\\[data-side\\=right\\]\\[data-collapsible\\=offcanvas\\]_\\&\\]\\:-left-2 {\n    [data-side=right][data-collapsible=offcanvas] & {\n      left: calc(var(--spacing) * -2);\n    }\n  }\n  .\\[\\[data-side\\=right\\]\\[data-state\\=collapsed\\]_\\&\\]\\:cursor-w-resize {\n    [data-side=right][data-state=collapsed] & {\n      cursor: w-resize;\n    }\n  }\n  .\\[a\\&\\]\\:hover\\:bg-accent {\n    a& {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--accent);\n        }\n      }\n    }\n  }\n  .\\[a\\&\\]\\:hover\\:bg-destructive\\/90 {\n    a& {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--destructive);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--destructive) 90%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .\\[a\\&\\]\\:hover\\:bg-primary\\/90 {\n    a& {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--primary);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--primary) 90%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .\\[a\\&\\]\\:hover\\:bg-secondary\\/90 {\n    a& {\n      &:hover {\n        @media (hover: hover) {\n          background-color: var(--secondary);\n          @supports (color: color-mix(in lab, red, red)) {\n            background-color: color-mix(in oklab, var(--secondary) 90%, transparent);\n          }\n        }\n      }\n    }\n  }\n  .\\[a\\&\\]\\:hover\\:text-accent-foreground {\n    a& {\n      &:hover {\n        @media (hover: hover) {\n          color: var(--accent-foreground);\n        }\n      }\n    }\n  }\n}\n:root {\n  --background: oklch(1 0 0);\n  --foreground: oklch(0.145 0 0);\n  --card: oklch(1 0 0);\n  --card-foreground: oklch(0.145 0 0);\n  --popover: oklch(1 0 0);\n  --popover-foreground: oklch(0.145 0 0);\n  --primary: oklch(0.205 0 0);\n  --primary-foreground: oklch(0.985 0 0);\n  --secondary: oklch(0.97 0 0);\n  --secondary-foreground: oklch(0.205 0 0);\n  --muted: oklch(0.97 0 0);\n  --muted-foreground: oklch(0.556 0 0);\n  --accent: oklch(0.97 0 0);\n  --accent-foreground: oklch(0.205 0 0);\n  --destructive: oklch(0.577 0.245 27.325);\n  --destructive-foreground: oklch(0.577 0.245 27.325);\n  --success: oklch(50.8% 0.118 165.612);\n  --border: oklch(0.922 0 0);\n  --input: oklch(0.922 0 0);\n  --ring: oklch(0.708 0 0);\n  --chart-1: oklch(0.646 0.222 41.116);\n  --chart-2: oklch(0.6 0.118 184.704);\n  --chart-3: oklch(0.398 0.07 227.392);\n  --chart-4: oklch(0.828 0.189 84.429);\n  --chart-5: oklch(0.769 0.188 70.08);\n  --radius: 0.625rem;\n  --sidebar: oklch(0.985 0 0);\n  --sidebar-foreground: oklch(0.145 0 0);\n  --sidebar-primary: oklch(0.205 0 0);\n  --sidebar-primary-foreground: oklch(0.985 0 0);\n  --sidebar-accent: oklch(0.97 0 0);\n  --sidebar-accent-foreground: oklch(0.205 0 0);\n  --sidebar-border: oklch(0.922 0 0);\n  --sidebar-ring: oklch(0.708 0 0);\n  --font-weight-bold: 700;\n}\n.dark {\n  --background: oklch(0.145 0 0);\n  --foreground: oklch(0.985 0 0);\n  --card: oklch(0.145 0 0);\n  --card-foreground: oklch(0.985 0 0);\n  --popover: oklch(0.145 0 0);\n  --popover-foreground: oklch(0.985 0 0);\n  --primary: oklch(0.985 0 0);\n  --primary-foreground: oklch(0.205 0 0);\n  --secondary: oklch(0.269 0 0);\n  --secondary-foreground: oklch(0.985 0 0);\n  --muted: oklch(0.269 0 0);\n  --muted-foreground: oklch(0.708 0 0);\n  --accent: oklch(0.269 0 0);\n  --accent-foreground: oklch(0.985 0 0);\n  --destructive: oklch(0.396 0.141 25.723);\n  --destructive-foreground: oklch(0.637 0.237 25.331);\n  --success: oklch(50.8% 0.118 165.612);\n  --border: oklch(0.269 0 0);\n  --input: oklch(0.269 0 0);\n  --ring: oklch(0.439 0 0);\n  --chart-1: oklch(0.488 0.243 264.376);\n  --chart-2: oklch(0.696 0.17 162.48);\n  --chart-3: oklch(0.769 0.188 70.08);\n  --chart-4: oklch(0.627 0.265 303.9);\n  --chart-5: oklch(0.645 0.246 16.439);\n  --sidebar: oklch(0.205 0 0);\n  --sidebar-foreground: oklch(0.985 0 0);\n  --sidebar-primary: oklch(0.488 0.243 264.376);\n  --sidebar-primary-foreground: oklch(0.985 0 0);\n  --sidebar-accent: oklch(0.269 0 0);\n  --sidebar-accent-foreground: oklch(0.985 0 0);\n  --sidebar-border: oklch(0.269 0 0);\n  --sidebar-ring: oklch(0.439 0 0);\n}\n@layer base {\n  * {\n    border-color: var(--border);\n    outline-color: var(--ring);\n    @supports (color: color-mix(in lab, red, red)) {\n      outline-color: color-mix(in oklab, var(--ring) 50%, transparent);\n    }\n  }\n  body {\n    background-color: var(--background);\n    color: var(--foreground);\n  }\n}\n@layer base {\n  ::after, ::before, ::backdrop, ::file-selector-button {\n    border-color: var(--border);\n  }\n  * {\n    min-width: calc(var(--spacing) * 0);\n  }\n  html {\n    text-rendering: optimizelegibility;\n  }\n  body {\n    min-height: 100dvh;\n  }\n  input::placeholder, textarea::placeholder {\n    color: var(--muted-foreground);\n  }\n  button:not(:disabled), [role=\"button\"]:not(:disabled) {\n    cursor: pointer;\n  }\n}\n.shiki {\n  background-color: var(--shiki-light-bg);\n  color: var(--shiki-light);\n  border-color: var(--border);\n}\n.shiki span {\n  color: var(--shiki-light);\n}\n.dark .shiki {\n  background-color: var(--shiki-dark-bg);\n  color: var(--shiki-dark);\n}\n.dark .shiki span {\n  color: var(--shiki-dark);\n}\n.shiki code {\n  display: grid;\n  font-size: 13px;\n  counter-reset: line;\n}\n.shiki .line:before {\n  content: counter(line);\n  counter-increment: line;\n  margin-right: calc(var(--spacing) * 8);\n  display: inline-block;\n  width: calc(var(--spacing) * 4);\n  text-align: right;\n  color: var(--muted-foreground);\n}\n.shiki[title]:before {\n  content: attr(title);\n  margin-bottom: calc(var(--spacing) * 6);\n  display: inline-block;\n  text-align: right;\n  font-size: var(--text-sm);\n  line-height: var(--tw-leading, var(--text-sm--line-height));\n  color: var(--muted-foreground);\n}\n@keyframes scroll-left {\n  0% {\n    transform: translateX(0);\n  }\n  100% {\n    transform: translateX(-50%);\n  }\n}\n@keyframes scroll-right {\n  0% {\n    transform: translateX(-50%);\n  }\n  100% {\n    transform: translateX(0);\n  }\n}\n.animate-scroll-left {\n  animation: scroll-left 35s linear infinite;\n}\n.animate-scroll-right {\n  animation: scroll-right 35s linear infinite;\n}\n.mask-gradient {\n  mask-image: linear-gradient(\n    to right,\n    transparent 0%,\n    black 15%,\n    black 85%,\n    transparent 100%\n  );\n  -webkit-mask-image: linear-gradient(\n    to right,\n    transparent 0%,\n    black 15%,\n    black 85%,\n    transparent 100%\n  );\n}\n@property --tw-translate-x {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-translate-y {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-translate-z {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-scale-x {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-scale-y {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-scale-z {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 1;\n}\n@property --tw-rotate-x {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-rotate-y {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-rotate-z {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-skew-x {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-skew-y {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-space-y-reverse {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-space-x-reverse {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0;\n}\n@property --tw-border-style {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: solid;\n}\n@property --tw-gradient-position {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-gradient-from {\n  syntax: \"<color>\";\n  inherits: false;\n  initial-value: #0000;\n}\n@property --tw-gradient-via {\n  syntax: \"<color>\";\n  inherits: false;\n  initial-value: #0000;\n}\n@property --tw-gradient-to {\n  syntax: \"<color>\";\n  inherits: false;\n  initial-value: #0000;\n}\n@property --tw-gradient-stops {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-gradient-via-stops {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-gradient-from-position {\n  syntax: \"<length-percentage>\";\n  inherits: false;\n  initial-value: 0%;\n}\n@property --tw-gradient-via-position {\n  syntax: \"<length-percentage>\";\n  inherits: false;\n  initial-value: 50%;\n}\n@property --tw-gradient-to-position {\n  syntax: \"<length-percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-leading {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-font-weight {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-tracking {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ordinal {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-slashed-zero {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-numeric-figure {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-numeric-spacing {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-numeric-fraction {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-inset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-inset-ring-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-inset-ring-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-ring-inset {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ring-offset-width {\n  syntax: \"<length>\";\n  inherits: false;\n  initial-value: 0px;\n}\n@property --tw-ring-offset-color {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: #fff;\n}\n@property --tw-ring-offset-shadow {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: 0 0 #0000;\n}\n@property --tw-outline-style {\n  syntax: \"*\";\n  inherits: false;\n  initial-value: solid;\n}\n@property --tw-blur {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-brightness {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-contrast {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-grayscale {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-hue-rotate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-invert {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-opacity {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-saturate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-sepia {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow-color {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-drop-shadow-alpha {\n  syntax: \"<percentage>\";\n  inherits: false;\n  initial-value: 100%;\n}\n@property --tw-drop-shadow-size {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-blur {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-brightness {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-contrast {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-grayscale {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-hue-rotate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-invert {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-opacity {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-saturate {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-backdrop-sepia {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-duration {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-ease {\n  syntax: \"*\";\n  inherits: false;\n}\n@property --tw-content {\n  syntax: \"*\";\n  initial-value: \"\";\n  inherits: false;\n}\n@keyframes pulse {\n  50% {\n    opacity: 0.5;\n  }\n}\n@keyframes enter {\n  from {\n    opacity: var(--tw-enter-opacity,1);\n    transform: translate3d(var(--tw-enter-translate-x,0),var(--tw-enter-translate-y,0),0)scale3d(var(--tw-enter-scale,1),var(--tw-enter-scale,1),var(--tw-enter-scale,1))rotate(var(--tw-enter-rotate,0));\n  }\n}\n@keyframes exit {\n  to {\n    opacity: var(--tw-exit-opacity,1);\n    transform: translate3d(var(--tw-exit-translate-x,0),var(--tw-exit-translate-y,0),0)scale3d(var(--tw-exit-scale,1),var(--tw-exit-scale,1),var(--tw-exit-scale,1))rotate(var(--tw-exit-rotate,0));\n  }\n}\n@keyframes accordion-down {\n  from {\n    height: 0;\n  }\n  to {\n    height: var(--radix-accordion-content-height,var(--bits-accordion-content-height,var(--reka-accordion-content-height,auto)));\n  }\n}\n@keyframes accordion-up {\n  from {\n    height: var(--radix-accordion-content-height,var(--bits-accordion-content-height,var(--reka-accordion-content-height,auto)));\n  }\n  to {\n    height: 0;\n  }\n}\n@keyframes caret-blink {\n  0%,70%,100% {\n    opacity: 1;\n  }\n  20%,50% {\n    opacity: 0;\n  }\n}\n@keyframes accordion-down {\n  from {\n    height: 0;\n  }\n  to {\n    height: var(--radix-accordion-content-height);\n  }\n}\n@keyframes accordion-up {\n  from {\n    height: var(--radix-accordion-content-height);\n  }\n  to {\n    height: 0;\n  }\n}\n@layer properties {\n  @supports ((-webkit-hyphens: none) and (not (margin-trim: inline))) or ((-moz-orient: inline) and (not (color:rgb(from red r g b)))) {\n    *, ::before, ::after, ::backdrop {\n      --tw-translate-x: 0;\n      --tw-translate-y: 0;\n      --tw-translate-z: 0;\n      --tw-scale-x: 1;\n      --tw-scale-y: 1;\n      --tw-scale-z: 1;\n      --tw-rotate-x: initial;\n      --tw-rotate-y: initial;\n      --tw-rotate-z: initial;\n      --tw-skew-x: initial;\n      --tw-skew-y: initial;\n      --tw-space-y-reverse: 0;\n      --tw-space-x-reverse: 0;\n      --tw-border-style: solid;\n      --tw-gradient-position: initial;\n      --tw-gradient-from: #0000;\n      --tw-gradient-via: #0000;\n      --tw-gradient-to: #0000;\n      --tw-gradient-stops: initial;\n      --tw-gradient-via-stops: initial;\n      --tw-gradient-from-position: 0%;\n      --tw-gradient-via-position: 50%;\n      --tw-gradient-to-position: 100%;\n      --tw-leading: initial;\n      --tw-font-weight: initial;\n      --tw-tracking: initial;\n      --tw-ordinal: initial;\n      --tw-slashed-zero: initial;\n      --tw-numeric-figure: initial;\n      --tw-numeric-spacing: initial;\n      --tw-numeric-fraction: initial;\n      --tw-shadow: 0 0 #0000;\n      --tw-shadow-color: initial;\n      --tw-shadow-alpha: 100%;\n      --tw-inset-shadow: 0 0 #0000;\n      --tw-inset-shadow-color: initial;\n      --tw-inset-shadow-alpha: 100%;\n      --tw-ring-color: initial;\n      --tw-ring-shadow: 0 0 #0000;\n      --tw-inset-ring-color: initial;\n      --tw-inset-ring-shadow: 0 0 #0000;\n      --tw-ring-inset: initial;\n      --tw-ring-offset-width: 0px;\n      --tw-ring-offset-color: #fff;\n      --tw-ring-offset-shadow: 0 0 #0000;\n      --tw-outline-style: solid;\n      --tw-blur: initial;\n      --tw-brightness: initial;\n      --tw-contrast: initial;\n      --tw-grayscale: initial;\n      --tw-hue-rotate: initial;\n      --tw-invert: initial;\n      --tw-opacity: initial;\n      --tw-saturate: initial;\n      --tw-sepia: initial;\n      --tw-drop-shadow: initial;\n      --tw-drop-shadow-color: initial;\n      --tw-drop-shadow-alpha: 100%;\n      --tw-drop-shadow-size: initial;\n      --tw-backdrop-blur: initial;\n      --tw-backdrop-brightness: initial;\n      --tw-backdrop-contrast: initial;\n      --tw-backdrop-grayscale: initial;\n      --tw-backdrop-hue-rotate: initial;\n      --tw-backdrop-invert: initial;\n      --tw-backdrop-opacity: initial;\n      --tw-backdrop-saturate: initial;\n      --tw-backdrop-sepia: initial;\n      --tw-duration: initial;\n      --tw-ease: initial;\n      --tw-content: \"\";\n    }\n  }\n}\n"], "names": [], "mappings": "AACA;EAohNE;IACE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAphNJ;EAEE;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAFF;EA8GE;;;;;;;EAMA;;;;;;;;;;EASA;;;;;;EAKA;;;;EAIA;;;;;EAIA;;;;;;EAKA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;;;EAKA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;;;;;;;EAUA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;IACE;;;;IAEE;MAAgD;;;;;;EAKpD;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EA4zLA;;;;;EAGE;IAAgD;;;;;EAIlD;;;;;EAMA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;AAllMF;;AAAA;EA+PE;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;;;;;;;EAWA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAyB;;;;;EAGzB;IAAyB;;;;;EAGzB;IAAyB;;;;;EAGzB;IAAyB;;;;;EAGzB;IAAyB;;;;;EAI3B;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAGE;;;;;EAIA;;;;;;;;EAOA;;;;;;EAKA;;;;;EAIA;;;;EASA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;;EAMA;;;;;EAIA;;;;EAGA;;;;;;EAKA;;;;;;;EAMA;;;;;;;;;;;;EAWA;;;;EAGA;;;;EAGA;;;;;;;;;EAQA;;;;;EAIA;;;;;;;;;EAQA;;;;;EAIA;;;;;;;;;EAQA;;;;;EAIA;;;;;;;;EAOA;;;;;EAIA;;;;;EAIA;;;;;;EAKA;;;;;EAIA;;;;;;;;;;;;;EAYA;;;;;;EAKA;;;;EAMA;;;;EAMA;;;;;EAIA;;;;;EAIA;;;;EASA;;;;;;;;;;;;;;;;EAeA;;;;;;;;;;;;EAWA;;;;EAMA;;;;;;;;;EAQA;;;;;EAIA;;;;;;;;;EAQA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;;;EA4CA;;;;;EAIA;;;;;EAIA;;;;EAMA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAYA;;;;EAGA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAIF;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAMA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;;;EAMA;;;;;;;EAMA;;;;;;;EAMA;;;;;;;EAMA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAIE;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAOA;;;;;;EAMF;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;;EAKA;;;;;;EAEE;IAAgD;;;;;EAMlD;;;;;;EAEE;IAAgD;;;;;EAMlD;;;;;;EAEE;IAAgD;;;;;EAMlD;;;;;;EAEE;IAAgD;;;;;EAMlD;;;;;;EAKA;;;;;;EAEE;IAAgD;;;;;EAMlD;;;;;;EAEE;IAAgD;;;;;EAMlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;;EAEE;IAAgD;;;;;EAKlD;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAEE;IAAgD;;;;;EAIlD;;;;EAGA;;;;EAGA;;;;;EAGE;IAAgC;;;;;;EAKlC;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;;EAKA;;;;;EAIA;;;;EAGA;;;;;EAKA;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;;;EAKA;;;;EAGA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAsCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;EAkCA;;;;EAIA;;;;;EAIA;;;;;EAIA;;;;EAIA;;;;EAIE;;;;EAME;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAMzB;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAOE;IAAuB;;;;;EAMzB;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAQA;;;;EAQA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;;EAQE;;;;;EAOF;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAME;IAAuB;;;;;;EAQvB;IAAuB;;;;;;;;EAUvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;IAErB;MAAgD;;;;;;EASlD;IAAuB;;;;;;EAQvB;IAAuB;;;;;IAErB;MAAgD;;;;;;EASlD;IAAuB;;;;;;EAQvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;EAOvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;;;EAQvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAQlD;IAAuB;;;;;;EAQvB;IACE;;;;;EAQF;IACE;;;;;;EAQJ;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;;EAGE;IAAgC;;;;;;EAOlC;;;;;EAMA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;;EAMA;;;;;EAGE;IAAgC;;;;;;EAOlC;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAEE;IAAgD;;;;;EAQ9C;IAAuB;;;;;EAQzB;;;;EAOA;;;;EAOA;;;;EAEE;IAAgD;;;;;EAOpD;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAME;;;;;EAQA;;;;;EAQA;;;;;EAQA;;;;;;EASA;;;;;;EAQF;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAME;;;;EAEE;IAAgD;;;;;EAQlD;;;;EAQE;;;;;EAUA;;;;;EASF;;;;;;;EAUA;;;;EAOA;;;;EAOA;;;;EAMF;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAMA;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAME;;;;EAOA;;;;EAQA;;;;EAOF;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAMA;;;;EAMA;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAME;;;;EAOA;;;;EAQA;;;;EASE;IAAuB;;;;;EASvB;IAAuB;;;;;EASvB;IAAuB;;;;;EAQzB;;;;EAMF;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAME;;;;EAEE;IAAgD;;;;;EAQlD;;;;EAMF;;;;;EAMA;;;;;EAOE;;;;;EAOF;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;IAAwC;;;;IAEtC;MAAgD;;;;;;EAMlD;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;IAEvB;MAAgD;;;;;;EAMlD;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;;EAMzB;IAEI;MAAuB;;;;MAErB;QAAgD;;;;;;;EASpD;IAAyB;;;;;EAOzB;IAAyB;;;;;EAM3B;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;EAKzB;IACE;;;;;EAMF;IACE;;;;;EAMF;IACE;;;;;EAMF;IACE;;;;;;EAOF;IAEI;;;;;EAOJ;IACE;;;;;;EAOF;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;;EAMzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;IAAyB;;;;;EAKzB;;;;;;;EAQA;;;;;;;EAQA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;;EAMA;;;;;;;;;;;;;;;;;;;;;EAwBI;IAAuB;;;;IAErB;MAAgD;;;;;;EAUlD;IAAuB;;;;IAErB;MAAgD;;;;;;EASpD;;;;EAEE;IAAgD;;;;;EAQlD;;;;EAEE;IAAgD;;;;;EAShD;;;;EAEE;IAAgD;;;;;EASpD;;;;EAOA;;;;EAEE;IAAgD;;;;;EAQlD;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAOA;;;;EAEE;IAAgD;;;;;EAShD;;;;EAEE;IAAgD;;;;;EAQtD;;;;EAKA;;;;EAEE;IAAgD;;;;;EAMlD;;;;EAKA;;;;EAKA;;;;;EAGE;IAAgC;;;;;;EAOlC;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAGE;IAAgC;;;;;;EAOlC;;;;EAKA;;;;;EAGE;IAAgC;;;;;;EAOlC;;;;EAKA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAME;;;;;EAQA;;;;;EAOF;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAOI;;;;EASA;;;;EASA;;;;EASA;;;;EAOJ;;;;EAKA;;;;;EAMA;;;;EAKA;;;;;;EAOA;;;;EAKA;;;;;EAMA;;;;;EAMA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;;EAMA;;;;EAKA;;;;EAKA;;;;EAME;;;;;EAOF;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAKA;;;;EAOI;IAAuB;;;;;EASvB;IAAuB;;;;IAErB;MAAgD;;;;;;EAUlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAUlD;IAAuB;;;;IAErB;MAAgD;;;;;;EAUlD;IAAuB;;;;;;AAO/B;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAqCA;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;;AAoEA;;;;;;AAKA;;;;AAGA;;;;;AAIA;;;;AAGA;;;;;;AAKA;;;;;;;;;;AASA;;;;;;;;;;AASA;;;;;;;;;;AAQA;;;;;;;;;;AAQA;;;;AAGA;;;;AAGA;;;;;AAgBA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;AAIA;;;;;;AAKA;;;;;;AAKA;;;;;;;AAMA;;;;;;;AA8BA;;;;;;;;;;AAQA;;;;;;;;;;AAhBA", "debugId": null}}, {"offset": {"line": 7356, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/geist%401.4.2_next%4015.3.2_%40op_71a80cc80e74b567b143c58fb994ca22/node_modules/geist/dist/geistmono_8e2790ea.module.css"], "sourcesContent": ["@font-face {\n    font-family: 'Geist<PERSON><PERSON>';\n    src: url('@vercel/turbopack-next/internal/font/local/font?{%22path%22:%22./fonts/geist-mono/GeistMono-Variable.woff2%22,%22preload%22:true,%22has_size_adjust%22:false}') format('woff2');\n    font-display: swap;\n    font-weight: 100 900;\n}\n\n\n.className {\n    font-family: 'GeistMono', ui-monospace, SFMono-Regular, Roboto Mono, Menlo, Monaco, Liberation Mono, DejaVu Sans Mono, Courier New, monospace;\n    \n}\n.variable {\n    --font-geist-mono: 'GeistM<PERSON>', ui-monospace, SFMono-Regular, Roboto Mono, Menlo, Monaco, Liberation Mono, DejaVu Sans Mono, Courier New, monospace;\n}\n\n"], "names": [], "mappings": "AAAA;;;;;;;AAQA;;;;AAIA", "ignoreList": [0], "debugId": null}}, {"offset": {"line": 7373, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/node_modules/.pnpm/geist%401.4.2_next%4015.3.2_%40op_71a80cc80e74b567b143c58fb994ca22/node_modules/geist/dist/geistsans_81192321.module.css"], "sourcesContent": ["@font-face {\n    font-family: 'GeistSans';\n    src: url('@vercel/turbopack-next/internal/font/local/font?{%22path%22:%22./fonts/geist-sans/Geist-Variable.woff2%22,%22preload%22:true,%22has_size_adjust%22:true}') format('woff2');\n    font-display: swap;\n    font-weight: 100 900;\n}\n\n@font-face {\n    font-family: 'GeistSans Fallback';\n    src: local(\"Arial\");\n    ascent-override: 85.83%;\ndescent-override: 20.53%;\nline-gap-override: 9.33%;\nsize-adjust: 107.19%;\n\n}\n\n.className {\n    font-family: 'GeistSans', 'GeistSans Fallback';\n    \n}\n.variable {\n    --font-geist-sans: 'GeistSans', 'GeistSans Fallback';\n}\n\n"], "names": [], "mappings": "AAAA;;;;;;;AAOA;;;;;;;;;AAUA;;;;AAIA", "ignoreList": [0], "debugId": null}}]}