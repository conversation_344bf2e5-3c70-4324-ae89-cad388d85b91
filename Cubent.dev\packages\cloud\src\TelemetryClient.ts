import { TelemetryEventName, type TelemetryEvent, rooCodeTelemetryEventSchema } from "@cubent/types"
import { BaseTelemetryClient } from "@cubent/telemetry"

import { getRooCodeApiUrl } from "./Config"
import { AuthService } from "./AuthService"
import { SettingsService } from "./SettingsService"

export class TelemetryClient extends BaseTelemetryClient {
	constructor(
		private authService: AuthService,
		private settingsService: SettingsService,
		debug = false,
	) {
		super(
			{
				type: "exclude",
				events: [TelemetryEventName.TASK_CONVERSATION_MESSAGE],
			},
			debug,
		)
	}

	private async fetch(path: string, options: RequestInit) {
		if (!this.authService.isAuthenticated()) {
			console.warn(`[TelemetryClient#fetch] User not authenticated - skipping telemetry event`)
			return
		}

		const token = this.authService.getSessionToken()

		if (!token) {
			console.error(`[TelemetryClient#fetch] Unauthorized: No session token available.`)
			return
		}

		const response = await fetch(`${getRooCodeApiUrl()}/api/${path}`, {
			...options,
			headers: { Authorization: `Bearer ${token}`, "Content-Type": "application/json" },
		})

		if (!response.ok) {
			console.error(
				`[TelemetryClient#fetch] ${options.method} ${path} -> ${response.status} ${response.statusText}`,
			)
		} else {
			console.log(`[TelemetryClient#fetch] Successfully sent telemetry event to ${path}`)
		}
	}

	public override async capture(event: TelemetryEvent) {
		if (!this.isTelemetryEnabled() || !this.isEventCapturable(event.event)) {
			if (this.debug) {
				console.info(`[TelemetryClient#capture] Skipping event: ${event.event}`)
			}

			return
		}

		// Debug: Log authentication status for LLM_COMPLETION events
		if (event.event === TelemetryEventName.LLM_COMPLETION) {
			console.log(`[TelemetryClient#capture] LLM_COMPLETION event - Auth status: ${this.authService.isAuthenticated()}, Has token: ${!!this.authService.getSessionToken()}`)
		}

		const payload = {
			type: event.event,
			properties: await this.getEventProperties(event),
		}

		if (this.debug || event.event === TelemetryEventName.LLM_COMPLETION) {
			console.info(`[TelemetryClient#capture] ${event.event} - ${JSON.stringify(payload)}`)
		}

		const result = rooCodeTelemetryEventSchema.safeParse(payload)

		if (!result.success) {
			console.error(
				`[TelemetryClient#capture] Invalid telemetry event: ${result.error.message} - ${JSON.stringify(payload)}`,
			)

			return
		}

		try {
			// Send LLM_COMPLETION events to the working track-usage endpoint
			if (result.data.type === 'LLM_COMPLETION') {
				const properties = result.data.properties;
				const trackingData = {
					modelId: properties.modelId || 'unknown',
					provider: properties.apiProvider || 'unknown',
					configName: properties.mode || 'agent',
					cubentUnits: 0, // LLM completions don't consume Cubent units directly
					tokensUsed: (properties.inputTokens || 0) + (properties.outputTokens || 0),
					inputTokens: properties.inputTokens || 0,
					outputTokens: properties.outputTokens || 0,
					costAccrued: properties.cost || 0,
					requestsMade: 1,
					timestamp: Date.now(),
					sessionId: properties.taskId,
					feature: 'llm-completion',
					language: 'typescript', // Default language
					metadata: {
						appName: properties.appName,
						appVersion: properties.appVersion,
						vscodeVersion: properties.vscodeVersion,
						platform: properties.platform,
						editorName: properties.editorName,
						mode: properties.mode,
						diffStrategy: properties.diffStrategy,
						isSubtask: properties.isSubtask,
						cacheWriteTokens: properties.cacheWriteTokens,
						cacheReadTokens: properties.cacheReadTokens,
						originalEventType: 'LLM_COMPLETION'
					}
				};

				console.log(`[TelemetryClient#fetch] Sending LLM_COMPLETION to track-usage:`, trackingData);
				await this.fetch(`extension/track-usage`, { method: "POST", body: JSON.stringify(trackingData) });
			} else {
				// Send other events to the events endpoint (if it exists)
				await this.fetch(`events`, { method: "POST", body: JSON.stringify(result.data) });
			}
		} catch (error) {
			console.error(`[TelemetryClient#capture] Error sending telemetry event: ${error}`)
		}
	}

	public override updateTelemetryState(_didUserOptIn: boolean) {}

	public override isTelemetryEnabled(): boolean {
		return true
	}

	protected override isEventCapturable(eventName: TelemetryEventName): boolean {
		// Ensure that this event type is supported by the telemetry client
		if (!super.isEventCapturable(eventName)) {
			return false
		}

		// Only record message telemetry if a cloud account is present and explicitly configured to record messages
		if (eventName === TelemetryEventName.TASK_MESSAGE) {
			return this.settingsService.getSettings()?.cloudSettings?.recordTaskMessages || false
		}

		// Other telemetry types are capturable at this point
		return true
	}

	public override async shutdown() {}
}
