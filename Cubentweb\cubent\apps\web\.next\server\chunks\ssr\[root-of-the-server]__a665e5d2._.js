module.exports = {

"[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/headers.js [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.resolve().then(() => {
        return parentImport("[project]/node_modules/.pnpm/next@15.3.2_@babel+core@7.2_09efc9edc30971f6ad8d5c21a0ab8f1f/node_modules/next/headers.js [app-rsc] (ecmascript)");
    });
});
}}),
"[project]/packages/cms/.basehub/next-toolbar/client-conditional-renderer-KQINRCBN.js [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/packages_cms__basehub_next-toolbar_client-conditional-renderer-KQINRCBN_b27f7c74.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/packages/cms/.basehub/next-toolbar/client-conditional-renderer-KQINRCBN.js [app-rsc] (ecmascript)");
    });
});
}}),
"[externals]/node:inspector [external] (node:inspector, cjs, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/[externals]_node:inspector_10d23a46._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[externals]/node:inspector [external] (node:inspector, cjs)");
    });
});
}}),
"[project]/packages/internationalization/dictionaries/en.json (json, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/packages_internationalization_dictionaries_en_json_0e87ed86._.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/packages/internationalization/dictionaries/en.json (json)");
    });
});
}}),
"[project]/packages/cms/.basehub/react-pump/client-pump-WYUPTPKD.js [app-rsc] (ecmascript, async loader)": ((__turbopack_context__) => {

var { g: global, __dirname } = __turbopack_context__;
{
__turbopack_context__.v((parentImport) => {
    return Promise.all([
  "server/chunks/ssr/packages_cms__basehub_react-pump_client-pump-WYUPTPKD_3351563b.js"
].map((chunk) => __turbopack_context__.l(chunk))).then(() => {
        return parentImport("[project]/packages/cms/.basehub/react-pump/client-pump-WYUPTPKD.js [app-rsc] (ecmascript)");
    });
});
}}),

};