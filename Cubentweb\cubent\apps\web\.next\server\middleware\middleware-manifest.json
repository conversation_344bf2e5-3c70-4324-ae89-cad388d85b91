{"sorted_middleware": [], "middleware": {"/": {"files": ["server/edge/chunks/_69b6a41a._.js", "server/edge/chunks/ec4b9_zod_dist_esm_cbcb71bd._.js", "server/edge/chunks/eec21_@clerk_shared_dist_40b2e982._.js", "server/edge/chunks/c67f4_@clerk_backend_dist_d8cc056d._.js", "server/edge/chunks/25c57_@clerk_nextjs_dist_esm_1ca17405._.js", "server/edge/chunks/node_modules__pnpm_1aac59bc._.js", "server/edge/chunks/[root-of-the-server]__6bc990ed._.js", "server/edge/chunks/apps_web_edge-wrapper_b13a772c.js"], "name": "middleware", "page": "/", "matchers": [{"regexp": "/:nextData(_next/data/[^/]{1,})?/((?!_next/static|_next/image|images|ingest|favicon.ico).*){(\\\\.json)}?", "originalSource": "/((?!_next/static|_next/image|images|ingest|favicon.ico).*)"}], "wasm": [], "assets": [], "env": {"__NEXT_BUILD_ID": "development", "NEXT_SERVER_ACTIONS_ENCRYPTION_KEY": "JFmE98NiJ/xqhp3pvYld3ls8dYeXUKqhINpTXPj7HIQ=", "__NEXT_PREVIEW_MODE_ID": "2ae41698a566816772a46b524420c28d", "__NEXT_PREVIEW_MODE_ENCRYPTION_KEY": "5c8dfe0d09444de1e433a261ff8b2c5472ed2cdcdd6e778efc99ab31393bd6b4", "__NEXT_PREVIEW_MODE_SIGNING_KEY": "7eddce8dc0f3844b72068fe31626f4c834025e15a58b2507d16c9675bf1fbf62"}}}, "instrumentation": null, "functions": {}}