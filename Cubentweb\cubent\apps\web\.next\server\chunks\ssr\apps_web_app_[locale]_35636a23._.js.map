{"version": 3, "sources": [], "sections": [{"offset": {"line": 7, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/apple-icon.tsx"], "sourcesContent": ["import { ImageResponse } from 'next/og'\n\nexport const runtime = 'edge'\n\nexport const size = {\n  width: 180,\n  height: 180,\n}\n\nexport const contentType = 'image/png'\n\nexport default function AppleIcon() {\n  return new ImageResponse(\n    (\n      <div\n        style={{\n          fontSize: 24,\n          background: '#000000',\n          width: '100%',\n          height: '100%',\n          display: 'flex',\n          alignItems: 'center',\n          justifyContent: 'center',\n        }}\n      >\n        {/* Cubent Logo SVG - larger for Apple icon */}\n        <svg\n          width=\"120\"\n          height=\"120\"\n          viewBox=\"0 0 24 24\"\n          xmlns=\"http://www.w3.org/2000/svg\"\n        >\n          <g transform=\"translate(12.5, 12) scale(0.0296, 0.0296) translate(-315, -330)\">\n            {/* Top face */}\n            <path\n              d=\"M 304.59375 3.09375 L 31.457031 149.265625 L 314.679688 301.847656 L 595.179688 147.667969 L 325.085938 3.574219 C 323.539062 2.667969 321.902344 1.96875 320.175781 1.480469 C 318.453125 0.992188 316.691406 0.722656 314.902344 0.683594 C 313.109375 0.640625 311.339844 0.824219 309.59375 1.230469 C 307.847656 1.640625 306.179688 2.261719 304.59375 3.09375 Z\"\n              fill=\"#ffffff\"\n            />\n            {/* Right face */}\n            <path\n              d=\"M 611.667969 512.703125 C 612.527344 512.246094 613.351562 511.730469 614.140625 511.160156 C 614.929688 510.589844 615.675781 509.972656 616.382812 509.300781 C 617.085938 508.628906 617.742188 507.914062 618.347656 507.152344 C 618.957031 506.390625 619.507812 505.59375 620.007812 504.757812 C 620.503906 503.921875 620.945312 503.058594 621.328125 502.164062 C 621.707031 501.265625 622.027344 500.351562 622.28125 499.410156 C 622.539062 498.472656 622.730469 497.519531 622.855469 496.554688 C 622.980469 495.589844 623.042969 494.621094 623.035156 493.648438 L 623.035156 181.769531 L 336.613281 339.308594 L 336.613281 659.515625 Z\"\n              fill=\"#ffffff\"\n              opacity=\"0.7\"\n            />\n            {/* Left face */}\n            <path\n              d=\"M 6.480469 493.007812 C 6.476562 493.988281 6.539062 494.960938 6.664062 495.929688 C 6.792969 496.902344 6.984375 497.855469 7.242188 498.800781 C 7.496094 499.746094 7.816406 500.667969 8.195312 501.570312 C 8.578125 502.46875 9.015625 503.339844 9.515625 504.183594 C 10.011719 505.023438 10.5625 505.828125 11.171875 506.597656 C 11.777344 507.363281 12.433594 508.085938 13.136719 508.765625 C 13.839844 509.445312 14.585938 510.074219 15.375 510.652344 C 16.164062 511.230469 16.988281 511.753906 17.847656 512.222656 L 293.222656 659.515625 L 293.**********.308594 L 6.**********.808594 Z\"\n              fill=\"#ffffff\"\n              opacity=\"0.6\"\n            />\n          </g>\n        </svg>\n      </div>\n    ),\n    {\n      ...size,\n    }\n  )\n}\n"], "names": [], "mappings": ";;;;;;;AAAA;;;AAEO,MAAM,UAAU;AAEhB,MAAM,OAAO;IAClB,OAAO;IACP,QAAQ;AACV;AAEO,MAAM,cAAc;AAEZ,SAAS;IACtB,OAAO,IAAI,yOAAA,CAAA,gBAAa,eAEpB,6VAAC;QACC,OAAO;YACL,UAAU;YACV,YAAY;YACZ,OAAO;YACP,QAAQ;YACR,SAAS;YACT,YAAY;YACZ,gBAAgB;QAClB;kBAGA,cAAA,6VAAC;YACC,OAAM;YACN,QAAO;YACP,SAAQ;YACR,OAAM;sBAEN,cAAA,6VAAC;gBAAE,WAAU;;kCAEX,6VAAC;wBACC,GAAE;wBACF,MAAK;;;;;;kCAGP,6VAAC;wBACC,GAAE;wBACF,MAAK;wBACL,SAAQ;;;;;;kCAGV,6VAAC;wBACC,GAAE;wBACF,MAAK;wBACL,SAAQ;;;;;;;;;;;;;;;;;;;;;cAMlB;QACE,GAAG,IAAI;IACT;AAEJ", "debugId": null}}, {"offset": {"line": 93, "column": 0}, "map": {"version": 3, "sources": ["file:///C:/Users/<USER>/Documents/2%20FOLDERS%20FOR%20CUBENT/Cubentweb/cubent/apps/web/app/%5Blocale%5D/apple-icon--metadata.js"], "sourcesContent": ["import { contentType, runtime, size } from \"./apple-icon.tsx\"\nimport { fillMetadataSegment } from 'next/dist/lib/metadata/get-metadata-route'\n\nconst imageModule = { contentType, runtime, size }\n\nexport default async function (props) {\n    const { __metadata_id__: _, ...params } = await props.params\n    const imageUrl = fillMetadataSegment(\"/[locale]\", params, \"apple-icon\")\n\n    const { generateImageMetadata } = imageModule\n\n    function getImageMetadata(imageMetadata, idParam) {\n        const data = {\n            alt: imageMetadata.alt,\n            type: imageMetadata.contentType || 'image/png',\n            url: imageUrl + (idParam ? ('/' + idParam) : '') + \"?af065cace01115f4\",\n        }\n        const { size } = imageMetadata\n        if (size) {\n            data.sizes = size.width + \"x\" + size.height;\n        }\n        return data\n    }\n\n    if (generateImageMetadata) {\n        const imageMetadataArray = await generateImageMetadata({ params })\n        return imageMetadataArray.map((imageMetadata, index) => {\n            const idParam = (imageMetadata.id || index) + ''\n            return getImageMetadata(imageMetadata, idParam)\n        })\n    } else {\n        return [getImageMetadata(imageModule, '')]\n    }\n}\n"], "names": [], "mappings": ";;;AAAA;AACA;;;AAEA,MAAM,cAAc;IAAE,aAAA,kJAAA,CAAA,cAAW;IAAE,SAAA,kJAAA,CAAA,UAAO;IAAE,MAAA,kJAAA,CAAA,OAAI;AAAC;AAElC,8CAAgB,KAAK;IAChC,MAAM,EAAE,iBAAiB,CAAC,EAAE,GAAG,QAAQ,GAAG,MAAM,MAAM,MAAM;IAC5D,MAAM,WAAW,CAAA,GAAA,0RAAA,CAAA,sBAAmB,AAAD,EAAE,aAAa,QAAQ;IAE1D,MAAM,EAAE,qBAAqB,EAAE,GAAG;IAElC,SAAS,iBAAiB,aAAa,EAAE,OAAO;QAC5C,MAAM,OAAO;YACT,KAAK,cAAc,GAAG;YACtB,MAAM,cAAc,WAAW,IAAI;YACnC,KAAK,WAAW,CAAC,UAAW,MAAM,UAAW,EAAE,IAAI;QACvD;QACA,MAAM,EAAE,IAAI,EAAE,GAAG;QACjB,IAAI,MAAM;YACN,KAAK,KAAK,GAAG,KAAK,KAAK,GAAG,MAAM,KAAK,MAAM;QAC/C;QACA,OAAO;IACX;IAEA,IAAI,uBAAuB;QACvB,MAAM,qBAAqB,MAAM,sBAAsB;YAAE;QAAO;QAChE,OAAO,mBAAmB,GAAG,CAAC,CAAC,eAAe;YAC1C,MAAM,UAAU,CAAC,cAAc,EAAE,IAAI,KAAK,IAAI;YAC9C,OAAO,iBAAiB,eAAe;QAC3C;IACJ,OAAO;QACH,OAAO;YAAC,iBAAiB,aAAa;SAAI;IAC9C;AACJ", "debugId": null}}]}