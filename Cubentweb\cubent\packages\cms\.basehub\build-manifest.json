{"generatedAt": "2025-07-04T16:55:31.330Z", "sdkVersion": "8.2.7", "inputHash": "3c7bdafacbea1fb62df366f186c2a17c", "schemaHash": "8b48f9bad782bff685c55c300b7eb17d", "resolvedRef": {"repoHash": "57ec52db", "type": "branch", "ref": "main", "createSuggestedBranchLink": null, "id": "KluwvFPvKCxusUOmSQG4q", "name": "main", "git": null, "createdAt": "2025-06-16T00:30:26.760Z", "archivedAt": null, "archivedBy": null, "headCommitId": "qNNz4p8JMipdRXk4579YJ", "isDefault": true, "deletedAt": null, "workingRootBlockId": "a8Oul5Re6jsffvG4Ab5XZ"}}