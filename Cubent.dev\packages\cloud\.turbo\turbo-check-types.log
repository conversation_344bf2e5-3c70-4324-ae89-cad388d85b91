
> @cubent/cloud@0.0.0 check-types C:\Users\<USER>\Documents\2 FOLDERS FOR CUBENT\Cubent.dev\packages\cloud
> tsc --noEmit

src/AuthService.ts(348,9): error TS2393: Duplicate function implementation.
src/AuthService.ts(464,9): error TS2393: Duplicate function implementation.
src/TelemetryClient.ts(129,4): error TS2339: Property 'inputTokens' does not exist on type 'Record<string, any> | undefined'.
src/TelemetryClient.ts(130,4): error TS2339: Property 'outputTokens' does not exist on type 'Record<string, any> | undefined'.
src/TelemetryClient.ts(131,4): error TS2339: Property 'cacheReadTokens' does not exist on type 'Record<string, any> | undefined'.
src/TelemetryClient.ts(132,4): error TS2339: Property 'cacheWriteTokens' does not exist on type 'Record<string, any> | undefined'.
src/TelemetryClient.ts(133,4): error TS2339: Property 'cost' does not exist on type 'Record<string, any> | undefined'.
src/TelemetryClient.ts(134,4): error TS2339: Property 'modelId' does not exist on type 'Record<string, any> | undefined'.
src/TelemetryClient.ts(135,4): error TS2339: Property 'apiProvider' does not exist on type 'Record<string, any> | undefined'.
 ELIFECYCLE  Command failed with exit code 2.
